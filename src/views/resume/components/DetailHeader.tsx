'use client';

import React, { useState, useCallback, useEffect, useRef } from 'react';
import { Button } from '@/components/ui/button';
import Image from 'next/image';
import Link from 'next/link';
import { useRouter } from 'next/navigation';
import EditIcon from '@/assets/svg/edit-icon.svg';
import { useModuleStore } from '@/store/useModuleStore';
import { formatDate } from '@/lib/utils';
import ResumeNameDialog from '@/components/Dialog/ResumeNameDialog';
import StandaloneAuth from '@/components/Auth/StandaloneAuth';
import MembershipWithDownloadCouponDialog from '@/components/MembershipWithDownloadCouponDialog';

import { toast } from 'sonner';
import { resumeApi } from '@/api/client/resume';
import { useUserStore } from '@/store/useUserStore';
import { userApi } from '@/api/client/user';
import { UserType } from '@/api/client/types/user';
import LoginDialog from '@/components/Auth/LoginDialog';

import type { UserResponse } from '@/api/client/types/user';

interface DetailHeaderProps {
  onlineUsers?: number;
  initialUserInfo: UserResponse | null; // 用户信息参数
  hideOptimize?: boolean; // 是否隐藏一键优化按钮，默认为false
  backUrl?: string; // 返回按钮的链接，如果不传则默认返回首页
}

/**
 * 私有的头部组件
 * 用于detail页面的导航栏，包含logo、返回首页按钮和操作按钮
 * 所有操作方法都在组件内部处理
 */
export default function DetailHeader({
  onlineUsers = 128,
  initialUserInfo,
  hideOptimize = false,
  backUrl = '/'
}: DetailHeaderProps) {
  // 路由实例
  const router = useRouter();

  // 从store中获取简历数据
  const {
    resumeName,
    lastUpdatedAt,
    resumeId,
    updateResumeName,
    saveResume
  } = useModuleStore();

  // 从用户store中获取登录状态
  const { is_logged_in, user_type } = useUserStore();

  // 弹窗状态
  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const [isAuthDialogOpen, setIsAuthDialogOpen] = useState(false);
  const [isMembershipDialogOpen, setIsMembershipDialogOpen] = useState(false);

  // 保存按钮loading状态
  const [isSaving, setIsSaving] = useState(false);

  // 在线用户数量状态
  const [currentOnlineUsers, setCurrentOnlineUsers] = useState(onlineUsers);

  // 定时器引用
  const timerRef = useRef<NodeJS.Timeout | null>(null);
  const autoSaveTimerRef = useRef<NodeJS.Timeout | null>(null);

  // 用户活动状态
  const lastActivityRef = useRef<number>(Date.now());

  // 格式化最后保存时间 - 使用useState和useEffect确保只在客户端渲染
  const [lastSaveTime, setLastSaveTime] = useState<string>('');

  // 在客户端渲染时更新时间
  React.useEffect(() => {
    setLastSaveTime(formatDate(lastUpdatedAt));
  }, [lastUpdatedAt]);

  // 记录在线状态的方法
  const recordOnlineStatus = useCallback(async () => {
    try {
      const result = await resumeApi.recordOnline();
      setCurrentOnlineUsers(result.online_count);
    } catch (error) {
      // 静默失败，不显示错误提示，避免干扰用户
    }
  }, []);

  // 更新用户活动时间
  const updateActivity = useCallback(() => {
    lastActivityRef.current = Date.now();
  }, []);

  // 检查用户是否无操作并执行自动保存
  const checkAndAutoSave = useCallback(async () => {
    const now = Date.now();
    const timeSinceLastActivity = now - lastActivityRef.current;

    // 如果用户无操作时间超过30秒（30000毫秒）
    if (timeSinceLastActivity >= 30000) {
      // 检查登录状态，优先使用initialUserInfo中的状态，如果没有则使用store中的状态
      const isLoggedIn = initialUserInfo ? initialUserInfo.is_logged_in : is_logged_in;

      if (!isLoggedIn) {
        // 未登录，显示提示并打开登录弹窗
        toast.error('自动保存失败，登录后可自动保存', {
          position: 'top-center'
        });
        setIsAuthDialogOpen(true);
        return;
      }

      try {
        // 已登录，执行自动保存
        await saveResume();
        // 保存成功后重置活动时间，避免频繁保存
        lastActivityRef.current = Date.now();
      } catch (error) {
        // 自动保存失败时静默处理，不显示错误提示，避免干扰用户
      }
    }
  }, [initialUserInfo, is_logged_in, saveResume]);

  // 初始化在线状态定时器
  useEffect(() => {
    // 立即执行一次
    recordOnlineStatus();

    // 设置定时器，每30秒执行一次
    timerRef.current = setInterval(recordOnlineStatus, 30000);

    // 清理函数
    return () => {
      if (timerRef.current) {
        clearInterval(timerRef.current);
        timerRef.current = null;
      }
    };
  }, [recordOnlineStatus]);

  // 初始化用户活动监听和自动保存定时器
  useEffect(() => {
    // 监听用户活动的事件
    const events = ['mousedown', 'mousemove', 'keypress', 'scroll', 'touchstart', 'click'];

    // 添加事件监听器
    events.forEach(event => {
      document.addEventListener(event, updateActivity, true);
    });

    // 设置定时器，每5秒检查一次是否需要自动保存
    autoSaveTimerRef.current = setInterval(checkAndAutoSave, 5000);

    // 清理函数
    return () => {
      // 移除事件监听器
      events.forEach(event => {
        document.removeEventListener(event, updateActivity, true);
      });

      // 清理定时器
      if (autoSaveTimerRef.current) {
        clearInterval(autoSaveTimerRef.current);
        autoSaveTimerRef.current = null;
      }
    };
  }, [updateActivity, checkAndAutoSave]);



  // 处理保存
  const handleSave = async () => {
    setIsSaving(true);

    try {
      await saveResume();

      // 检查登录状态，优先使用initialUserInfo中的状态，如果没有则使用store中的状态
      const isLoggedIn = initialUserInfo ? initialUserInfo.is_logged_in : is_logged_in;

      if (!isLoggedIn) {
        // 未登录，保存完成后显示提示并打开登录弹窗
        toast.error('自动保存失败，登录后可自动保存', {
          position: 'top-center'
        });
        setIsAuthDialogOpen(true);
      } else {
        // 已登录，显示成功提示
        toast.success('简历已保存', {
          position: 'top-center'
        });
      }
    } catch (error) {
      toast.error('保存失败，请重试', {
        position: 'top-center'
      });
    } finally {
      setIsSaving(false);
    }
  };

  // 处理一键优化
  const handleOptimize = () => {
    if (!resumeId) {
      toast.error('简历ID不存在，无法进行优化', {
        position: 'top-center'
      });
      return;
    }

    // 检查登录状态，优先使用store中的最新状态（支付成功后会更新），如果store状态为默认值则使用initialUserInfo
    const isLoggedIn = is_logged_in || (initialUserInfo ? initialUserInfo.is_logged_in : false);

    if (!isLoggedIn) {
      // 未登录，显示提示并打开登录弹窗
      toast.error('请先登录后再使用一键优化功能', {
        position: 'top-center'
      });
      setIsAuthDialogOpen(true);
      return;
    }

    // 已登录，跳转到优化页面，带上resume_id参数
    router.push(`/youhua/?resume_id=${resumeId}`);
  };

  // 处理一键打分
  const handleScore = () => {
    if (!resumeId) {
      toast.error('简历ID不存在，无法进行打分', {
        position: 'top-center'
      });
      return;
    }

    // 检查登录状态，优先使用store中的最新状态（支付成功后会更新），如果store状态为默认值则使用initialUserInfo
    const isLoggedIn = is_logged_in || (initialUserInfo ? initialUserInfo.is_logged_in : false);

    if (!isLoggedIn) {
      // 未登录，显示提示并打开登录弹窗
      toast.error('请先登录后再使用一键打分功能', {
        position: 'top-center'
      });
      setIsAuthDialogOpen(true);
      return;
    }

    // 已登录，跳转到打分页面，带上resume_id参数
    router.push(`/zhenduan/?resume_id=${resumeId}`);
  };

  // 处理下载按钮点击
  const handleDownloadClick = async (e: React.MouseEvent) => {
    if (!resumeId) {
      toast.error('简历ID不存在，无法下载', {
        position: 'top-center'
      });
      return;
    }

    // 检查登录状态，优先使用store中的最新状态（支付成功后会更新），如果store状态为默认值则使用initialUserInfo
    const isLoggedIn = is_logged_in || (initialUserInfo ? initialUserInfo.is_logged_in : false);
    const currentUserType = user_type !== undefined ? user_type : (initialUserInfo ? initialUserInfo.user_type : user_type);

    if (!isLoggedIn) {
      e.preventDefault();
      setIsAuthDialogOpen(true);
      return;
    }

    // 检查会员状态
    if (currentUserType !== UserType.Member) {
      e.preventDefault();
      try {
        // 调用接口检查下载券数量
        const couponsData = await userApi.getDownloadCouponsCount();
        if (couponsData.count > 0) {
          // 有下载券，允许正常跳转
          window.location.href = `/make/download/${resumeId}`;
          return;
        }
      } catch (error) {
        console.error('获取下载券数量失败:', error);
      }
      // 没有下载券或获取失败，显示会员弹窗
      setIsMembershipDialogOpen(true);
      return;
    }

    // 都满足条件，允许正常跳转（不阻止默认行为）
  };



  // 移除未使用的函数
  // const handleAvatarClick = () => {
  //   // 这里可以添加头像点击的逻辑
  // };

  // 处理编辑文件名
  const handleEditFileName = useCallback(() => {
    setIsDialogOpen(true);
  }, []);

  // 处理确认修改文件名
  const handleConfirmRename = useCallback(async (newName: string) => {
    if (newName && newName !== resumeName) {
      try {
        await updateResumeName(newName);
        toast.success('简历名称已更新', {
          position: 'top-center'
        });
      } catch (error) {
        toast.error('更新简历名称失败，请重试', {
          position: 'top-center'
        });
      }
    }
  }, [resumeName, updateResumeName]);
  return (
    <div className="flex items-center justify-between">
      {/* 左侧区域：Logo和返回按钮 */}
      <div className="flex items-center gap-4">
        {/* Logo */}
        <Link href="/" className="flex items-center w-8 h-8 justify-center cursor-pointer hover:opacity-90 transition-opacity" title="熊猫简历">
          <Image
            src="/home/<USER>"
            alt="熊猫简历"
            width={32}
            height={32}
            className="h-8 w-8"
          />
        </Link>

        {/* 返回按钮 */}
        <Link
          href={backUrl}
          className="flex items-center justify-center w-8 h-8 border border-gray-200 rounded-lg hover:bg-gray-50 active:bg-gray-100 transition-colors cursor-pointer"
        >
          <svg xmlns="http://www.w3.org/2000/svg" width="8" height="14" viewBox="0 0 10 18" fill="none">
            <path
              fillRule="evenodd"
              clipRule="evenodd"
              d="M8.58166 0.863283L9.90137 2.20901L3.24171 8.99997L9.90136 15.7909L8.58166 17.1367L0.602293 8.99997L8.58166 0.863283Z"
              fill="#707191"
            />
          </svg>
        </Link>

        {/* 文件名和编辑图标 */}
        <div className="flex items-center gap-2 border border-gray-200 rounded-lg px-3 py-2 cursor-pointer hover:bg-gray-50 transition-colors" onClick={handleEditFileName}>
          <span className="text-sm font-medium">{resumeName}</span>
          <button
            className="cursor-pointer hover:opacity-80"
            onClick={(e) => {
              e.stopPropagation();
              handleEditFileName();
            }}
          >
            <EditIcon width={16} height={16} />
          </button>
        </div>

        {/* 简历名称编辑弹窗 */}
        <ResumeNameDialog
          isOpen={isDialogOpen}
          onClose={() => setIsDialogOpen(false)}
          onConfirm={handleConfirmRename}
          initialName={resumeName}
        />
      </div>

      {/* 中间区域：在线用户信息 */}
      <div className="flex-1 text-center">
        <span className="text-sm text-foreground">
          此时有 <span className="text-primary font-medium">{currentOnlineUsers}</span> 个小伙伴正在和你一起写简历🧑‍🤝‍🧑~
        </span>
      </div>

      {/* 右侧区域：操作按钮 */}
      <div className="flex items-center gap-3">
        <span className="text-sm text-foreground">上次保存 {lastSaveTime}</span>

        <Button
          variant="outline"
          size="sm"
          className="h-9 px-6 py-1 border-gray-200 hover:bg-gray-50 cursor-pointer transition-colors"
          onClick={handleSave}
          disabled={isSaving}
        >
          {isSaving ? '保存中...' : '保存'}
        </Button>

        {!hideOptimize && (
          <Button
            variant="outline"
            size="sm"
            className="h-9 px-6 py-1 border-gray-200 hover:bg-gray-50 cursor-pointer transition-colors"
            onClick={handleOptimize}
          >
            一键优化
          </Button>
        )}

        {!hideOptimize && (
          <Button
            variant="outline"
            size="sm"
            className="h-9 px-6 py-1 border-gray-200 hover:bg-gray-50 cursor-pointer transition-colors"
            onClick={handleScore}
          >
            一键打分
          </Button>
        )}

        {!hideOptimize && (
          <>
            {/* 预览按钮 */}
            <Link
              href={`/make/preview/${resumeId}/`}
              className="inline-flex items-center justify-center h-11 px-8 py-2 bg-primary hover:bg-purple-600 text-white cursor-pointer transition-colors rounded-lg text-base font-semibold shadow-md hover:shadow-lg"
            >
              预览
            </Link>

            {/* 下载按钮 */}
            <Link
              href={`/make/download/${resumeId}`}
              onClick={handleDownloadClick}
              className="inline-flex items-center justify-center h-11 px-8 py-2 bg-primary hover:bg-purple-600 text-white cursor-pointer transition-colors rounded-lg text-base font-semibold shadow-md hover:shadow-lg"
            >
              下载
            </Link>
          </>
        )}

        {/* 使用不依赖 AuthProvider 的 StandaloneAuth 组件 */}
        <StandaloneAuth initialUserInfo={initialUserInfo} />
      </div>

      {/* 登录弹窗 */}
      <LoginDialog
        isOpen={isAuthDialogOpen}
        onClose={() => setIsAuthDialogOpen(false)}
      />

      {/* 会员弹窗 */}
      <MembershipWithDownloadCouponDialog
        isOpen={isMembershipDialogOpen}
        onClose={() => setIsMembershipDialogOpen(false)}
      />
    </div>
  );
}
