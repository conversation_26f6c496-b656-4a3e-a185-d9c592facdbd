
import Link from 'next/link';
import Image from 'next/image';
import Navigation from './Navigation';
import MemberButton from './MemberButton';
import StandaloneAuth from '@/components/Auth/StandaloneAuth';
import { UserResponse } from '@/api/client/types/user';

interface NavItem {
  label: string;
  path: string;
}

interface HeaderProps {
  userInfo: UserResponse | null;
  /**
   * 是否显示导航菜单，默认为 true
   */
  showNavigation?: boolean;
}

const navItems: NavItem[] = [
  { label: '首页', path: '/' },
  { label: '简历模板', path: '/jianli/' },
  { label: 'AI简历生成', path: '/ai/' },
  { label: 'AI简历优化', path: '/youhua/' },
  { label: 'AI简历打分', path: '/zhenduan/' },
  // { label: 'Word模板', path: 'https://www.jlqnw.com/moulds?=pandaresume'},
  { label: '简历服务', path: '/fuwu/' },
];

export default function Header({ userInfo, showNavigation = true }: HeaderProps) {
  // 根据是否显示导航菜单来决定容器样式
  const containerClass = showNavigation
    ? "max-w-[1400px] h-full mx-auto px-6 flex items-center justify-between"
    : "h-full px-4 flex items-center justify-between w-full";

  return (
    <header className="w-full h-[68px] bg-white shadow-sm sticky top-0 z-40">
      <div className={containerClass}>
        {/* Logo 区域 */}
        <Link href="/" className="flex items-center" title="熊猫简历">
          <Image
            src="/home/<USER>"
            alt="熊猫简历"
            width={186}
            height={57}
            priority
          />
        </Link>

        {/* 导航菜单 - 桌面端 */}
        {showNavigation && <Navigation items={navItems} userLoggedIn={userInfo?.is_logged_in} />}
        
        {/* 用户区域 */}
        <div className="flex items-center gap-4">
          {/* <MemberButton /> */}
          <StandaloneAuth initialUserInfo={userInfo} />
        </div>

      </div>
    </header>
  );
}
