'use client';

import Link from 'next/link';
import { usePathname } from 'next/navigation';
import { useState } from 'react';
import { useUserStore } from '@/store/useUserStore';
import LoginDialog from '@/components/Auth/LoginDialog';

interface NavItem {
  label: string;
  path: string;
}

interface NavigationProps {
  items: NavItem[];
  userLoggedIn?: boolean; // 用户登录状态
}

export default function Navigation({ items, userLoggedIn }: NavigationProps) {
  const pathname = usePathname();
  const { is_logged_in } = useUserStore();
  const [isLoginDialogOpen, setIsLoginDialogOpen] = useState(false);

  // 需要登录才能访问的导航项路径
  const requireLoginPaths = ['/ai/', '/youhua/', '/zhenduan/'];

  // 判断导航项是否应该被选中
  const isActive = (itemPath: string) => {
    // 如果 pathname 为 null，返回 false
    if (!pathname) return false;

    // 首页路径需要精确匹配
    if (itemPath === '/') {
      return pathname === '/';
    }

    // 其他路径使用前缀匹配
    return pathname.startsWith(itemPath);
  };

  // 处理导航项点击
  const handleNavClick = (e: React.MouseEvent, itemPath: string) => {
    // 检查是否需要登录验证
    if (requireLoginPaths.includes(itemPath)) {
      // 优先使用传入的userLoggedIn，如果没有则使用store中的状态
      const isLoggedIn = userLoggedIn !== undefined ? userLoggedIn : is_logged_in;

      if (!isLoggedIn) {
        e.preventDefault(); // 阻止默认跳转
        setIsLoginDialogOpen(true); // 打开登录对话框
        return;
      }
    }
    // 如果不需要登录验证或用户已登录，则正常跳转（不阻止默认行为）
  };

  // 关闭登录对话框
  const handleCloseLoginDialog = () => {
    setIsLoginDialogOpen(false);
  };

  return (
    <>
      <nav className="flex items-center h-full px-2.5">
        {items.map((item) => (
          <Link
            key={item.path}
            href={item.path}
            title={`${item.label}`}
            target={item.path === '/' ? '_self' : '_blank'}
            rel={item.path === '/' ? undefined : 'noopener noreferrer'}
            className={`h-10 flex items-center px-4 text-[17px] font-semibold text-black relative cursor-pointer transition-all duration-200 rounded-full mx-1 hover:text-[var(--primary)] hover:bg-[rgba(138,92,247,0.05)] ${
              isActive(item.path) ? 'text-[var(--primary)] bg-[rgba(138,92,247,0.1)]' : ''
            }`}
            onClick={(e) => handleNavClick(e, item.path)}
          >
            {item.label}
          </Link>
        ))}
      </nav>

      {/* 登录对话框 */}
      <LoginDialog
        isOpen={isLoginDialogOpen}
        onClose={handleCloseLoginDialog}
      />
    </>
  );
}
