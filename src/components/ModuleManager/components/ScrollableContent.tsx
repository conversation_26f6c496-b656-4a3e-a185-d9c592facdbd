'use client';

import React from 'react';
import { useModuleHeaderHeight } from '@/hooks/useModuleHeaderHeight';

interface ScrollableContentProps {
  children: React.ReactNode;
  className?: string;
}

/**
 * 可滚动内容容器组件
 * 提供统一的滚动样式，避免页面出现滚动条
 * 使用固定高度，根据contentHeight设置
 */
export default function ScrollableContent({ children, className = '' }: ScrollableContentProps) {
  // 使用自定义hook获取contentHeight
  const { contentHeight } = useModuleHeaderHeight();

  return (
    <div
      className={`overflow-y-auto ${className}`}
      style={{
        height: contentHeight,
        msOverflowStyle: 'none',  /* IE and Edge */
        scrollbarWidth: 'none',   /* Firefox */
        position: 'relative',     /* 确保正确的堆叠上下文 */
        zIndex: 1                 /* 确保内容在上层 */
      }}
    >
      {/* 添加一个内部样式，隐藏WebKit浏览器的滚动条 */}
      <style jsx>{`
        div::-webkit-scrollbar {
          display: none;
        }
      `}</style>
      {children}
    </div>
  );
}
