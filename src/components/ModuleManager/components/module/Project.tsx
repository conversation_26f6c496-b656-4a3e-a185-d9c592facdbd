'use client';

import React, { useState, useEffect } from 'react';
import Tip from '../Tip';
import ModuleNavigator from '@/components/ModuleNavigator';
import ScrollableContent from '../ScrollableContent';
import ModuleHeader from '@/components/ModuleHeader';
import Image from 'next/image';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { cn } from '@/lib/utils'; // 用于条件类名
import MarkdownEditor from '@/components/MarkdownEditor';
import AIGenerateButtons from '@/components/AIGenerateButtons';
import YearMonthPicker from '@/components/YearMonthPicker';
import {
  DndContext,
  closestCenter,
  KeyboardSensor,
  PointerSensor,
  useSensor,
  useSensors,
  DragEndEvent,
  DragStartEvent,
  DragOverlay,
  MeasuringStrategy,
} from '@dnd-kit/core';
import {
  SortableContext,
  sortableKeyboardCoordinates,
  useSortable,
  verticalListSortingStrategy,
} from '@dnd-kit/sortable';
import { CSS } from '@dnd-kit/utilities';
import { useModuleStore, type ProjectItem as StoreProjectItem } from '@/store/useModuleStore';
import ItemDeleteConfirmDialog from '../ItemDeleteConfirmDialog';

// 本地项目经历项目类型（用于表单状态）
interface ProjectItem {
  id: string;
  name: string;
  role: string;
  company: string;
  start_month: string;
  end_month: string;
  desc: string;
  index: number;
}



// 可排序的项目经历项组件
const SortableProjectItem = ({
  item,
  expandedItems,
  toggleExpanded,
  deleteProjectItem,
  projectItems,
  setProjectItems
}: {
  item: ProjectItem;
  expandedItems: Record<string, boolean>;
  toggleExpanded: (itemId: string) => void;
  deleteProjectItem: (itemId: string) => void;
  projectItems: ProjectItem[];
  setProjectItems: React.Dispatch<React.SetStateAction<ProjectItem[]>>;
}) => {
  const {
    attributes,
    listeners,
    setNodeRef,
    transform,
    transition,
    isDragging,
  } = useSortable({ id: item.id });

  const style = {
    transform: CSS.Transform.toString(transform),
    transition,
    opacity: isDragging ? 0.5 : 1,
    zIndex: isDragging ? 999 : 'auto',
    position: 'relative' as const,
    touchAction: 'none',
  };

  return (
    <div ref={setNodeRef} style={style} className="mb-4 bg-w">
      <div
        className="flex items-center justify-between w-full text-base text-[#2E2F66] bg-white p-4 rounded-md"
        {...attributes}
      >
        <div className="flex items-center gap-3">
          <div className="w-[137px] text-ellipsis overflow-hidden whitespace-nowrap" title={item.name}>
            {item.name}
          </div>
        </div>
        <div>
          {item.start_month} <span>- {item.end_month}</span>
        </div>
        <div className="flex gap-5 items-center">
          <div
            className={cn(
              "cursor-grab active:cursor-grabbing p-1 rounded-md transition-colors",
              "hover:bg-gray-100 hover:text-primary"
            )}
            {...listeners}
            title="拖拽排序"
          >
            <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 20 20" fill="none">
              <path fillRule="evenodd" clipRule="evenodd" d="M7.19958 15.0532C7.47572 15.0532 7.69958 14.8294 7.69958 14.5532L7.69957 5.44624C7.69957 5.23032 7.56097 5.03879 7.35587 4.9713C7.15077 4.9038 6.92551 4.97559 6.79728 5.14932L4.55673 8.18498C4.39274 8.40716 4.43992 8.72021 4.66209 8.88419C4.88427 9.04818 5.19732 9.001 5.36131 8.77882L6.69958 6.96565L6.69958 14.5532C6.69958 14.8294 6.92343 15.0532 7.19958 15.0532Z" fill="currentColor"></path>
              <path fillRule="evenodd" clipRule="evenodd" d="M12.8004 4.94678C12.5243 4.94678 12.3004 5.17063 12.3004 5.44678L12.3004 14.5538C12.3004 14.7697 12.439 14.9612 12.6441 15.0287C12.8492 15.0962 13.0745 15.0244 13.2027 14.8507L15.4433 11.815C15.6073 11.5928 15.5601 11.2798 15.3379 11.1158C15.1157 10.9518 14.8027 10.999 14.6387 11.2212L13.3004 13.0344L13.3004 5.44678C13.3004 5.17063 13.0766 4.94678 12.8004 4.94678Z" fill="currentColor"></path>
            </svg>
          </div>
          <div
            className={`cursor-pointer ${projectItems.length <= 1 ? 'opacity-50' : 'hover:text-red-500'}`}
            onClick={(e) => {
              e.stopPropagation();
              if (projectItems.length > 1) {
                deleteProjectItem(item.id);
              }
            }}
            title={projectItems.length <= 1 ? "至少保留一个项目经历" : "删除此项目经历"}
          >
            <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 20 20" fill="none">
              <path fillRule="evenodd" clipRule="evenodd" d="M6.7251 8.77979C7.00124 8.77979 7.2251 9.00364 7.2251 9.27979V14.7341H12.7746V9.27979C12.7746 9.00364 12.9985 8.77979 13.2746 8.77979C13.5508 8.77979 13.7746 9.00364 13.7746 9.27979V15.2341C13.7746 15.5103 13.5508 15.7341 13.2746 15.7341H6.7251C6.44896 15.7341 6.2251 15.5103 6.2251 15.2341V9.27979C6.2251 9.00364 6.44896 8.77979 6.7251 8.77979Z" fill="currentColor"></path>
              <path fillRule="evenodd" clipRule="evenodd" d="M4.76807 6.78955C4.76807 6.51341 4.99192 6.28955 5.26807 6.28955L14.732 6.28955C15.0082 6.28955 15.232 6.51341 15.232 6.78955C15.232 7.06569 15.0082 7.28955 14.732 7.28955L5.26807 7.28955C4.99192 7.28955 4.76807 7.06569 4.76807 6.78955Z" fill="currentColor"></path>
              <path fillRule="evenodd" clipRule="evenodd" d="M7.76147 4.76611C7.76147 4.48997 7.98533 4.26611 8.26147 4.26611H11.7384C12.0145 4.26611 12.2384 4.48997 12.2384 4.76611C12.2384 5.04226 12.0145 5.26611 11.7384 5.26611H8.26147C7.98533 5.26611 7.76147 5.04226 7.76147 4.76611Z" fill="currentColor"></path>
              <path fillRule="evenodd" clipRule="evenodd" d="M8.91357 8.95068C9.18972 8.95068 9.41357 9.17454 9.41357 9.45068V13.1917C9.41357 13.4679 9.18972 13.6917 8.91357 13.6917C8.63743 13.6917 8.41357 13.4679 8.41357 13.1917V9.45068C8.41357 9.17454 8.63743 8.95068 8.91357 8.95068Z" fill="currentColor"></path>
              <path fillRule="evenodd" clipRule="evenodd" d="M11.0864 8.95068C11.3626 8.95068 11.5864 9.17454 11.5864 9.45068V13.1917C11.5864 13.4679 11.3626 13.6917 11.0864 13.6917C10.8103 13.6917 10.5864 13.4679 10.5864 13.1917V9.45068C10.5864 9.17454 10.8103 8.95068 11.0864 8.95068Z" fill="currentColor"></path>
            </svg>
          </div>
          <div
            className="cursor-pointer"
            onClick={(e) => {
              e.stopPropagation();
              toggleExpanded(item.id);
            }}
            style={{ transform: expandedItems[item.id] ? 'rotate(180deg)' : 'rotate(0deg)' }}
          >
            <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 20 20" fill="none">
              <path fillRule="evenodd" clipRule="evenodd" d="M4.99805 7.85151L5.825 7L9.99805 11.297L14.1711 7L14.998 7.85151L9.99805 13L4.99805 7.85151Z" fill="#707191"></path>
            </svg>
          </div>
        </div>
      </div>

      {expandedItems[item.id] && (
        <div className="pt-[22px]" style={{ transition: 'max-height 0.3s' }}>
          <form className="grid grid-cols-2 gap-6" onSubmit={(e) => e.preventDefault()}>
            {/* 项目名称 - 占用一整行 */}
            <div className="space-y-2 col-span-2">
              <Label htmlFor={`name_${item.id}`} className="text-base">项目名称</Label>
              <Input
                id={`name_${item.id}`}
                type="text"
                placeholder="输入项目名称，如：企业级中台系统"
                value={item.name}
                onChange={(e) => {
                  const updatedItems = projectItems.map(proj =>
                    proj.id === item.id ? {...proj, name: e.target.value} : proj
                  );
                  setProjectItems(updatedItems);
                }}
                className="bg-white text-base md:text-base"
              />
            </div>

            {/* 我的角色 */}
            <div className="space-y-2">
              <Label htmlFor={`role_${item.id}`} className="text-base">我的角色</Label>
              <Input
                id={`role_${item.id}`}
                type="text"
                placeholder="如：前端技术负责人"
                value={item.role}
                onChange={(e) => {
                  const updatedItems = projectItems.map(proj =>
                    proj.id === item.id ? {...proj, role: e.target.value} : proj
                  );
                  setProjectItems(updatedItems);
                }}
                className="bg-white text-base md:text-base"
              />
            </div>

            {/* 所属公司 */}
            <div className="space-y-2">
              <Label htmlFor={`company_${item.id}`} className="text-base">所属公司</Label>
              <Input
                id={`company_${item.id}`}
                type="text"
                placeholder="如：腾讯科技有限公司"
                value={item.company}
                onChange={(e) => {
                  const updatedItems = projectItems.map(proj =>
                    proj.id === item.id ? {...proj, company: e.target.value} : proj
                  );
                  setProjectItems(updatedItems);
                }}
                className="bg-white text-base md:text-base"
              />
            </div>

            {/* 开始时间 - 使用年月选择器 */}
            <YearMonthPicker
              id={`start_month_${item.id}`}
              label="开始时间"
              placeholder={`如：${new Date().getFullYear() - 2}-03`}
              value={item.start_month}
              onChange={(value) => {
                const updatedItems = projectItems.map(proj =>
                  proj.id === item.id ? {...proj, start_month: value} : proj
                );
                setProjectItems(updatedItems);
              }}
            />

            {/* 结束时间 - 使用年月选择器 */}
            <YearMonthPicker
              id={`end_month_${item.id}`}
              label="结束时间"
              placeholder={`如：${new Date().getFullYear() - 1}-01，或选择'至今'`}
              value={item.end_month}
              onChange={(value) => {
                const updatedItems = projectItems.map(proj =>
                  proj.id === item.id ? {...proj, end_month: value} : proj
                );
                setProjectItems(updatedItems);
              }}
            />

            {/* 项目描述 */}
            <div className="col-span-2">
              <div className="space-y-2">
                <Label htmlFor={`desc_${item.id}`} className="text-base">项目描述</Label>
                <MarkdownEditor
                  value={item.desc}
                  onChange={(value) => {
                    const updatedItems = projectItems.map(proj =>
                      proj.id === item.id ? {...proj, desc: value} : proj
                    );
                    setProjectItems(updatedItems);
                  }}
                  height={200}
                  placeholder="项目描述应包含项目介绍、你的主要任务和成绩结果三部分。

填写示例:

项目介绍：简要描述项目内容和规模。
主要任务：详细说明你在项目中的职责和贡献。
成绩结果：量化你的成果，如通过XX提升效率XX%，将销售额提高XX%。"
                />
              </div>
              <AIGenerateButtons
                className="w-full"
                markdownContent={item.desc}
                projectInfo={{
                  name: item.name,
                  role: item.role,
                  company: item.company
                }}
                onContentGenerated={(content) => {
                  const updatedItems = projectItems.map(proj =>
                    proj.id === item.id ? {...proj, desc: content} : proj
                  );
                  setProjectItems(updatedItems);
                }}
              />
            </div>
          </form>
        </div>
      )}
    </div>
  );
};

export default function Project() {
  // 从store中获取project模块数据
  const { modules, updateModuleItem, activeIndex, setActiveIndex } = useModuleStore();
  const projectModule = modules['project'];

  // 使用ref来跟踪是否是本地状态更新导致的变化
  const isLocalUpdate = React.useRef(false);
  // 使用ref来跟踪是否是首次渲染
  const isFirstRender = React.useRef(true);

  // 从store数据转换为本地表单数据
  const convertStoreToLocal = (storeData: StoreProjectItem[]): ProjectItem[] => {
    return storeData.map(item => ({
      id: item.id,
      name: item.name?.value || '',
      role: item.role?.value || '',
      company: item.company?.value || '',
      start_month: item.start_month?.value || '',
      end_month: item.end_month?.value || '',
      desc: item.desc?.value || '',
      index: item.index
    }));
  };

  // 从本地表单数据转换为store数据
  const convertLocalToStore = (localData: ProjectItem[]): StoreProjectItem[] => {
    return localData.map(item => ({
      id: item.id,
      name: { label: "项目名称", value: item.name },
      role: { label: "担任角色", value: item.role },
      company: { label: "所属公司", value: item.company },
      start_month: { label: "开始时间", value: item.start_month },
      end_month: { label: "结束时间", value: item.end_month },
      desc: { label: "项目描述", value: item.desc },
      index: item.index
    }));
  };

  // 使用store中的数据，如果没有则使用空数组
  const [projectItems, setProjectItems] = useState<ProjectItem[]>(() => {
    if (projectModule?.item && Array.isArray(projectModule.item) && projectModule.item.length > 0) {
      return convertStoreToLocal(projectModule.item as StoreProjectItem[]);
    }
    return [];
  });

  // 初始化：如果store中没有数据，则创建一个默认的空项目
  useEffect(() => {
    if (projectModule && (!projectModule.item || !Array.isArray(projectModule.item) || projectModule.item.length === 0)) {
      // 创建一个默认的空项目经历项目
      const defaultProjectItem: ProjectItem = {
        id: 'proj-1',
        name: '',
        role: '',
        company: '',
        start_month: '',
        end_month: '',
        desc: '',
        index: 0
      };

      // 标记为本地更新
      isLocalUpdate.current = true;
      // 更新本地状态
      setProjectItems([defaultProjectItem]);
      // 更新store中的数据
      updateModuleItem('project', convertLocalToStore([defaultProjectItem]));
      // 重置标记
      setTimeout(() => {
        isLocalUpdate.current = false;
      }, 0);
    }
  }, [projectModule, updateModuleItem]);

  // 当store中的数据变化时，更新本地状态，但仅当不是由本地更新触发的
  useEffect(() => {
    if (!isLocalUpdate.current && projectModule?.item && Array.isArray(projectModule.item) && projectModule.item.length > 0) {
      setProjectItems(convertStoreToLocal(projectModule.item as StoreProjectItem[]));
    }
  }, [projectModule?.item]);

  // 当本地状态变化时，更新store，但需要防止无限循环
  useEffect(() => {
    // 如果是首次渲染，标记为非首次渲染并返回
    if (isFirstRender.current) {
      isFirstRender.current = false;
      return;
    }

    // 仅当不是从store更新本地状态时才更新store
    if (projectModule && !isLocalUpdate.current) {
      // 标记为本地更新
      isLocalUpdate.current = true;
      // 将本地数据转换为store格式并更新
      updateModuleItem('project', convertLocalToStore(projectItems));
      // 重置标记
      setTimeout(() => {
        isLocalUpdate.current = false;
      }, 0);
    }
  }, [projectItems, projectModule, updateModuleItem]);

  // 展开/折叠状态 - 使用对象来跟踪每个项目经历项目的展开状态
  const [expandedItems, setExpandedItems] = useState<Record<string, boolean>>(() => {
    // 根据 activeIndex 数组确定默认展开的项目
    if (activeIndex.length > 0 && projectItems.length > 0) {
      const initialExpanded: Record<string, boolean> = {};
      activeIndex.forEach(index => {
        if (projectItems[index]) {
          initialExpanded[projectItems[index].id] = true;
        }
      });
      return initialExpanded;
    }
    // 如果没有 activeIndex 或对应项目不存在，默认展开第一个项目
    return projectItems.length > 0 ? { [projectItems[0].id]: true } : {};
  });

  // 监听 activeIndex 变化，更新展开状态
  useEffect(() => {
    if (activeIndex.length > 0 && projectItems.length > 0) {
      // 根据 activeIndex 数组展开对应的项目
      const newExpandedItems: Record<string, boolean> = {};
      activeIndex.forEach(index => {
        if (projectItems[index]) {
          newExpandedItems[projectItems[index].id] = true;
        }
      });
      setExpandedItems(newExpandedItems);
    }
  }, [activeIndex, projectItems]);

  // 拖拽状态
  const [activeId, setActiveId] = useState<string | null>(null);

  // 获取当前拖拽的项目
  const activeItem = activeId ? projectItems.find(item => item.id === activeId) : null;

  // 删除确认对话框状态
  const [deleteConfirmOpen, setDeleteConfirmOpen] = useState(false);
  const [itemToDelete, setItemToDelete] = useState<{id: string, name: string} | null>(null);

  // 切换特定项目的展开/折叠状态
  const toggleExpanded = (itemId: string) => {
    const itemIndex = projectItems.findIndex(item => item.id === itemId);
    if (itemIndex === -1) return;

    // 先更新展开状态
    setExpandedItems(prev => {
      const newExpanded = {
        ...prev,
        [itemId]: !prev[itemId]
      };

      // 在下一个事件循环中更新 activeIndex，避免在渲染过程中调用 setState
      setTimeout(() => {
        const currentActiveIndex = [...activeIndex];
        if (newExpanded[itemId]) {
          // 展开：添加到 activeIndex
          if (!currentActiveIndex.includes(itemIndex)) {
            currentActiveIndex.push(itemIndex);
            setActiveIndex(currentActiveIndex);
          }
        } else {
          // 折叠：从 activeIndex 移除
          const indexToRemove = currentActiveIndex.indexOf(itemIndex);
          if (indexToRemove > -1) {
            currentActiveIndex.splice(indexToRemove, 1);
            setActiveIndex(currentActiveIndex);
          }
        }
      }, 0);

      return newExpanded;
    });
  };

  // 处理拖拽开始
  const handleDragStart = (event: DragStartEvent) => {
    const { active } = event;
    setActiveId(active.id as string);
  };

  // 处理拖拽结束
  const handleDragEnd = (event: DragEndEvent) => {
    const { active, over } = event;

    if (over && active.id !== over.id && Array.isArray(projectItems)) {
      // 获取排序后的项目经历数组
      const oldIndex = projectItems.findIndex(item => item.id === active.id);
      const newIndex = projectItems.findIndex(item => item.id === over.id);

      // 确保索引有效
      if (oldIndex !== -1 && newIndex !== -1) {
        // 更新项目经历项目的顺序
        const updatedItems = [...projectItems];
        const [movedItem] = updatedItems.splice(oldIndex, 1);
        updatedItems.splice(newIndex, 0, movedItem);

        // 更新所有项目的索引
        const reindexedItems = updatedItems.map((item, index) => ({
          ...item,
          index
        }));

        // 更新状态
        setProjectItems(reindexedItems);
      }
    }

    // 重置拖拽状态
    setActiveId(null);
  };

  // 添加新的项目经历
  const addProjectItem = () => {
    // 确保 projectItems 是数组
    const safeItems = Array.isArray(projectItems) ? projectItems : [];

    // 生成唯一ID
    const newId = `proj-${Date.now()}`;

    // 创建新的项目经历项目
    const newProjectItem: ProjectItem = {
      id: newId,
      name: '',
      role: '',
      company: '',
      start_month: '',
      end_month: '',
      desc: '',
      index: safeItems.length // 设置索引为当前列表长度
    };

    // 添加到列表中
    setProjectItems([...safeItems, newProjectItem]);

    // 自动展开新添加的项目
    setExpandedItems(prev => ({
      ...prev,
      [newId]: true
    }));
  };

  // 显示删除确认对话框
  const showDeleteConfirm = (itemId: string) => {
    // 确保 projectItems 是数组
    if (!Array.isArray(projectItems)) {
      return;
    }

    // 如果只剩下一个项目，不允许删除
    if (projectItems.length <= 1) {
      return;
    }

    const item = projectItems.find(item => item.id === itemId);
    if (item) {
      setItemToDelete({
        id: itemId,
        name: item.name || '项目经历'
      });
      setDeleteConfirmOpen(true);
    }
  };

  // 确认删除项目经历项目
  const confirmDeleteProjectItem = () => {
    if (!itemToDelete) return;

    // 从列表中移除项目
    const updatedItems = projectItems.filter(item => item.id !== itemToDelete.id);

    // 更新状态
    setProjectItems(updatedItems);

    // 从展开状态中移除该项目
    const newExpandedItems = { ...expandedItems };
    delete newExpandedItems[itemToDelete.id];
    setExpandedItems(newExpandedItems);

    // 重置删除状态
    setItemToDelete(null);
  };

  // 保留原有的删除函数以兼容现有代码
  const deleteProjectItem = (itemId: string) => {
    showDeleteConfirm(itemId);
  };

  // 定义提示项
  const tipItems = [
    {
      question: '一定要写项目经历吗？',
      answer: '如果之前的公司不是项目制，可以不写。\n当然自己单独做过的一些事情也可以算作项目，如和申请职位比较符合，建议写上'
    },
    {
      question: '如何写好一段项目经历？',
      answer: '一般由【项目介绍+你的主要任务+成绩结果】三部分组成\n项目介绍讲清楚项目内容和规模。\n重点是你的任务+结果：可参考工作经历万能公式：动作/手段+结果。\n示例：\n通过 XX 提升 XX 效率XX% ，将销售额提高XX%；\n通过数据分析，提出XX解决方案，成功将XX从XX提升至XX；\n如果外行人无法看出有多么厉害，加上对比解释，如：比去年同期提高xx%，较同期入职人员高出xx倍。'
    }
  ];

  // 确保 projectItems 是一个数组
  const safeProjectItems = Array.isArray(projectItems) ? projectItems : [];

  // 准备 sortable items 的 ID 列表
  const sortableItemIds = safeProjectItems.map(item => item.id);

  return (
    <div className="h-full">
      <div className="flex gap-4 h-full">
        <div className="w-48">
          <Tip
            title="项目经历怎么写？"
            description="还有疑惑? 立即反馈"
            tipContent={
              <>
                填写简历的核心思路就是——扬长避短，突出自己和岗位的匹配度。面试或者简历上有缺点没关系，反而更真实，但不要暴露硬伤。
              </>
            }
            items={tipItems}
          />
        </div>

        <div className="flex-1 rounded-md flex flex-col bg-white">
          {/* 使用 ScrollableContent 组件 */}
          <ScrollableContent>
            {/* 模块头部 */}
            <ModuleHeader
              id="project"
              icon={<Image src="/image/project.svg" alt="项目经历" width={24} height={24} />}
              title="项目经历"
              description="项目包括商业公司的具体项目、科研活动、商业竞赛、课程作业等等，非常适合展示你解决问题的能力💪"
            />

            {/* 表单内容区域 */}
            <div className="p-6 space-y-6 bg-[#f7f8fa] rounded-md">
              {/* 项目经历列表 */}
              <div className="flex-1">
                {/* 使用DndContext包装项目经历列表 */}
                <DndContext
                  sensors={useSensors(
                    useSensor(PointerSensor, {
                      activationConstraint: {
                        distance: 2, // 只需要拖动2px就会触发拖拽
                        tolerance: 3, // 增加容差，使拖拽更容易触发
                        delay: 0, // 无延迟
                      },
                    }),
                    useSensor(KeyboardSensor, {
                      coordinateGetter: sortableKeyboardCoordinates,
                    })
                  )}
                  collisionDetection={closestCenter}
                  onDragStart={handleDragStart}
                  onDragEnd={handleDragEnd}
                  measuring={{
                    droppable: {
                      strategy: MeasuringStrategy.Always,
                    },
                  }}
                >
                  <SortableContext
                    items={sortableItemIds}
                    strategy={verticalListSortingStrategy}
                  >
                    {safeProjectItems.map((item) => (
                      <SortableProjectItem
                        key={item.id}
                        item={item}
                        expandedItems={expandedItems}
                        toggleExpanded={toggleExpanded}
                        deleteProjectItem={deleteProjectItem}
                        projectItems={safeProjectItems}
                        setProjectItems={setProjectItems}
                      />
                    ))}
                  </SortableContext>

                  {/* 拖拽预览 */}
                  <DragOverlay>
                    {activeItem ? (
                      <div className="flex items-center justify-between w-full text-base text-[#2E2F66] bg-white p-4 rounded-md shadow-lg border-2 border-purple-200">
                        <div className="flex items-center gap-3">
                          <div className="w-[137px] text-ellipsis overflow-hidden whitespace-nowrap" title={activeItem.name}>
                            {activeItem.name || '未填写项目名称'}
                          </div>
                        </div>
                        <div>
                          {activeItem.start_month || '未填写'} <span>- {activeItem.end_month || '未填写'}</span>
                        </div>
                        <div className="flex gap-5 items-center">
                          <div className={cn(
                            "cursor-grab p-1 rounded-md bg-gray-100 text-primary"
                          )}>
                            <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 20 20" fill="none">
                              <path fillRule="evenodd" clipRule="evenodd" d="M7.19958 15.0532C7.47572 15.0532 7.69958 14.8294 7.69958 14.5532L7.69957 5.44624C7.69957 5.23032 7.56097 5.03879 7.35587 4.9713C7.15077 4.9038 6.92551 4.97559 6.79728 5.14932L4.55673 8.18498C4.39274 8.40716 4.43992 8.72021 4.66209 8.88419C4.88427 9.04818 5.19732 9.001 5.36131 8.77882L6.69958 6.96565L6.69958 14.5532C6.69958 14.8294 6.92343 15.0532 7.19958 15.0532Z" fill="currentColor"></path>
                              <path fillRule="evenodd" clipRule="evenodd" d="M12.8004 4.94678C12.5243 4.94678 12.3004 5.17063 12.3004 5.44678L12.3004 14.5538C12.3004 14.7697 12.439 14.9612 12.6441 15.0287C12.8492 15.0962 13.0745 15.0244 13.2027 14.8507L15.4433 11.815C15.6073 11.5928 15.5601 11.2798 15.3379 11.1158C15.1157 10.9518 14.8027 10.999 14.6387 11.2212L13.3004 13.0344L13.3004 5.44678C13.3004 5.17063 13.0766 4.94678 12.8004 4.94678Z" fill="currentColor"></path>
                            </svg>
                          </div>
                        </div>
                      </div>
                    ) : null}
                  </DragOverlay>
                </DndContext>
              </div>

            </div>

            {/* 添加项目经历按钮和跳转器 */}
            <div className="mt-6 flex justify-between items-center">
              <button
                type="button"
                onClick={addProjectItem}
                className="flex items-center justify-center gap-2 py-2 px-5 rounded-md transition-all bg-white text-[#333] border border-gray-200 hover:bg-gray-50 active:bg-gray-100 focus:outline-none focus:ring-2 focus:ring-gray-200 cursor-pointer shadow-sm whitespace-nowrap"
              >
                <svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
                  <path d="M8 4V12" stroke="#333" strokeWidth="1.5" strokeLinecap="round"/>
                  <path d="M4 8H12" stroke="#333" strokeWidth="1.5" strokeLinecap="round"/>
                </svg>
                <span className="text-base font-medium">项目经历</span>
              </button>

              <ModuleNavigator currentModuleId="project" />
            </div>
          </ScrollableContent>
        </div>
      </div>

      {/* 删除确认对话框 */}
      <ItemDeleteConfirmDialog
        open={deleteConfirmOpen}
        onOpenChange={setDeleteConfirmOpen}
        itemName={itemToDelete?.name || ''}
        itemType="项目经历"
        onConfirm={confirmDeleteProjectItem}
        disabled={projectItems.length <= 1}
        disabledReason="至少需要保留一个项目经历"
      />
    </div>
  );
}
