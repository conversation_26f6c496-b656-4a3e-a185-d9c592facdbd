'use client';

import React, { useState, useEffect } from 'react';
import Tip from '../Tip';
import ModuleNavigator from '@/components/ModuleNavigator';
import ScrollableContent from '../ScrollableContent';
import ModuleHeader from '@/components/ModuleHeader';
import Image from 'next/image';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
// 移除未使用的导入
// import {
//   Select,
//   SelectContent,
//   SelectItem,
//   SelectTrigger,
//   SelectValue
// } from '@/components/ui/select';
import { cn } from '@/lib/utils'; // 用于条件类名，在isDragging样式中使用
import MarkdownEditor from '@/components/MarkdownEditor';
import AIGenerateButtons from '@/components/AIGenerateButtons';
import YearMonthPicker from '@/components/YearMonthPicker';
import {
  DndContext,
  closestCenter,
  KeyboardSensor,
  PointerSensor,
  useSensor,
  useSensors,
  DragEndEvent,
  DragStartEvent,
  DragOverlay,
  MeasuringStrategy,
} from '@dnd-kit/core';
import {
  SortableContext,
  sortableKeyboardCoordinates,
  useSortable,
  verticalListSortingStrategy,
} from '@dnd-kit/sortable';
import { CSS } from '@dnd-kit/utilities';
import { useModuleStore, type WorkItem as StoreWorkItem } from '@/store/useModuleStore';
import ItemDeleteConfirmDialog from '../ItemDeleteConfirmDialog';

// 本地工作经历项目类型（用于表单状态）
interface WorkItem {
  id: string;
  company: string;
  department: string;
  city: string;
  job: string;
  start_month: string;
  end_month: string;
  desc: string;
  company_tags: string[];
  job_tags: string[];
  index: number;
}



// 可排序的工作经历项组件
const SortableWorkItem = ({
  item,
  expandedItems,
  toggleExpanded,
  deleteWorkItem,
  workItems,
  setWorkItems,
  customCompanyTagInputs,
  handleCustomCompanyTagInput,
  showCustomCompanyTagInput,
  setCustomCompanyTagInputs,
  customJobTagInputs,
  handleCustomJobTagInput,
  showCustomJobTagInput,
  setCustomJobTagInputs
}: {
  item: WorkItem;
  expandedItems: Record<string, boolean>;
  toggleExpanded: (itemId: string) => void;
  deleteWorkItem: (itemId: string) => void;
  workItems: WorkItem[];
  setWorkItems: React.Dispatch<React.SetStateAction<WorkItem[]>>;
  customCompanyTagInputs: Record<string, {isEditing: boolean, value: string}>;
  handleCustomCompanyTagInput: (itemId: string, value: string) => void;
  showCustomCompanyTagInput: (itemId: string) => void;
  setCustomCompanyTagInputs: React.Dispatch<React.SetStateAction<Record<string, {isEditing: boolean, value: string}>>>;
  customJobTagInputs: Record<string, {isEditing: boolean, value: string}>;
  handleCustomJobTagInput: (itemId: string, value: string) => void;
  showCustomJobTagInput: (itemId: string) => void;
  setCustomJobTagInputs: React.Dispatch<React.SetStateAction<Record<string, {isEditing: boolean, value: string}>>>;
}) => {
  // 使用dnd-kit的useSortable钩子
  const {
    attributes,
    listeners,
    setNodeRef,
    transform,
    transition,
    isDragging
  } = useSortable({
    id: item.id,
    data: {
      type: 'work-item',
      item
    }
  });

  // 设置样式
  const style = {
    transform: CSS.Transform.toString(transform),
    transition,
    opacity: isDragging ? 0.5 : 1,
    zIndex: isDragging ? 1000 : 1,
    position: 'relative' as const,
    touchAction: 'none',
  };

  return (
    <div ref={setNodeRef} style={style} className="mb-4 bg-w">
      <div
        className="flex items-center justify-between w-full text-base text-[#2E2F66] bg-white p-4 rounded-md cursor-pointer"
        onClick={() => toggleExpanded(item.id)}
        {...attributes}
      >
        <div className="flex items-center gap-3">
          <div className="w-[137px] text-ellipsis overflow-hidden whitespace-nowrap" title={item.company}>
            {item.company || '添加公司名称'}
          </div>
        </div>
        <div>
          {item.start_month} <span>- {item.end_month}</span>
        </div>
        <div className="flex gap-5 items-center">
          <div
            className={cn(
              "cursor-grab active:cursor-grabbing p-1 rounded-md transition-colors",
              "hover:bg-gray-100 hover:text-primary"
            )}
            {...listeners}
            title="拖拽排序"
          >
            <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 20 20" fill="none">
              <path fillRule="evenodd" clipRule="evenodd" d="M7.19958 15.0532C7.47572 15.0532 7.69958 14.8294 7.69958 14.5532L7.69957 5.44624C7.69957 5.23032 7.56097 5.03879 7.35587 4.9713C7.15077 4.9038 6.92551 4.97559 6.79728 5.14932L4.55673 8.18498C4.39274 8.40716 4.43992 8.72021 4.66209 8.88419C4.88427 9.04818 5.19732 9.001 5.36131 8.77882L6.69958 6.96565L6.69958 14.5532C6.69958 14.8294 6.92343 15.0532 7.19958 15.0532Z" fill="currentColor"></path>
              <path fillRule="evenodd" clipRule="evenodd" d="M12.8004 4.94678C12.5243 4.94678 12.3004 5.17063 12.3004 5.44678L12.3004 14.5538C12.3004 14.7697 12.439 14.9612 12.6441 15.0287C12.8492 15.0962 13.0745 15.0244 13.2027 14.8507L15.4433 11.815C15.6073 11.5928 15.5601 11.2798 15.3379 11.1158C15.1157 10.9518 14.8027 10.999 14.6387 11.2212L13.3004 13.0344L13.3004 5.44678C13.3004 5.17063 13.0766 4.94678 12.8004 4.94678Z" fill="currentColor"></path>
            </svg>
          </div>
          <div
            className={`cursor-pointer ${workItems.length <= 1 ? 'opacity-50' : 'hover:text-red-500'}`}
            onClick={(e) => {
              e.stopPropagation();
              if (workItems.length > 1) {
                deleteWorkItem(item.id);
              }
            }}
            title={workItems.length <= 1 ? "至少保留一个工作经历" : "删除此工作经历"}
          >
            <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 20 20" fill="none">
              <path fillRule="evenodd" clipRule="evenodd" d="M6.7251 8.77979C7.00124 8.77979 7.2251 9.00364 7.2251 9.27979V14.7341H12.7746V9.27979C12.7746 9.00364 12.9985 8.77979 13.2746 8.77979C13.5508 8.77979 13.7746 9.00364 13.7746 9.27979V15.2341C13.7746 15.5103 13.5508 15.7341 13.2746 15.7341H6.7251C6.44896 15.7341 6.2251 15.5103 6.2251 15.2341V9.27979C6.2251 9.00364 6.44896 8.77979 6.7251 8.77979Z" fill="currentColor"></path>
              <path fillRule="evenodd" clipRule="evenodd" d="M4.76807 6.78955C4.76807 6.51341 4.99192 6.28955 5.26807 6.28955L14.732 6.28955C15.0082 6.28955 15.232 6.51341 15.232 6.78955C15.232 7.06569 15.0082 7.28955 14.732 7.28955L5.26807 7.28955C4.99192 7.28955 4.76807 7.06569 4.76807 6.78955Z" fill="currentColor"></path>
              <path fillRule="evenodd" clipRule="evenodd" d="M7.76147 4.76611C7.76147 4.48997 7.98533 4.26611 8.26147 4.26611H11.7384C12.0145 4.26611 12.2384 4.48997 12.2384 4.76611C12.2384 5.04226 12.0145 5.26611 11.7384 5.26611H8.26147C7.98533 5.26611 7.76147 5.04226 7.76147 4.76611Z" fill="currentColor"></path>
              <path fillRule="evenodd" clipRule="evenodd" d="M8.91357 8.95068C9.18972 8.95068 9.41357 9.17454 9.41357 9.45068V13.1917C9.41357 13.4679 9.18972 13.6917 8.91357 13.6917C8.63743 13.6917 8.41357 13.4679 8.41357 13.1917V9.45068C8.41357 9.17454 8.63743 8.95068 8.91357 8.95068Z" fill="currentColor"></path>
              <path fillRule="evenodd" clipRule="evenodd" d="M11.0864 8.95068C11.3626 8.95068 11.5864 9.17454 11.5864 9.45068V13.1917C11.5864 13.4679 11.3626 13.6917 11.0864 13.6917C10.8103 13.6917 10.5864 13.4679 10.5864 13.1917V9.45068C10.5864 9.17454 10.8103 8.95068 11.0864 8.95068Z" fill="currentColor"></path>
            </svg>
          </div>
          <div
            className="cursor-pointer"
            onClick={(e) => {
              e.stopPropagation();
              toggleExpanded(item.id);
            }}
            style={{ transform: expandedItems[item.id] ? 'rotate(180deg)' : 'rotate(0deg)' }}
          >
            <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 20 20" fill="none">
              <path fillRule="evenodd" clipRule="evenodd" d="M4.99805 7.85151L5.825 7L9.99805 11.297L14.1711 7L14.998 7.85151L9.99805 13L4.99805 7.85151Z" fill="#707191"></path>
            </svg>
          </div>
        </div>
      </div>

      {/* 展开的表单内容 */}
      {expandedItems[item.id] && (
        <div className="pt-[22px]" style={{ transition: 'max-height 0.3s' }}>
          <form className="grid grid-cols-2 gap-6" onSubmit={(e) => e.preventDefault()}>
            {/* 公司和职位放在一行 */}
            <div className="space-y-2">
              <Label htmlFor={`company_${item.id}`} className="text-base">公司</Label>
              <Input
                id={`company_${item.id}`}
                type="text"
                placeholder="输入公司全称，如：腾讯科技有限公司"
                value={item.company}
                onChange={(e) => {
                  const updatedItems = workItems.map(work =>
                    work.id === item.id ? {...work, company: e.target.value} : work
                  );
                  setWorkItems(updatedItems);
                }}
                className="bg-white text-base md:text-base"
              />
              <div className="flex flex-wrap gap-2 mt-2">
                {/* 渲染所有公司标签 */}
                {(item.company_tags || []).map((tag) => (
                  <button
                    key={tag}
                    type="button"
                    onClick={() => {
                      // 移除标签
                      const updatedTags = (item.company_tags || []).filter(t => t !== tag);
                      const updatedItems = workItems.map(work =>
                        work.id === item.id ? {...work, company_tags: updatedTags} : work
                      );
                      setWorkItems(updatedItems);
                    }}
                    className="px-3 py-1 text-base rounded-md border border-primary bg-primary text-white transition-colors focus:outline-none focus:ring-2 focus:ring-purple-200 cursor-pointer"
                  >
                    {tag}
                  </button>
                ))}

                {/* 渲染未选中的预设公司标签 */}
                {['世界500强', '业内TOP3', '美股上市', '港股上市', 'A股上市', '独角兽', 'C轮融资'].filter(tag => !(item.company_tags || []).includes(tag)).map((tag) => (
                  <button
                    key={tag}
                    type="button"
                    onClick={() => {
                      // 添加标签
                      const updatedTags = [...(item.company_tags || []), tag];
                      const updatedItems = workItems.map(work =>
                        work.id === item.id ? {...work, company_tags: updatedTags} : work
                      );
                      setWorkItems(updatedItems);
                    }}
                    className="px-3 py-1 text-base rounded-md border border-gray-200 bg-white text-gray-700 hover:bg-gray-50 transition-colors focus:outline-none focus:ring-2 focus:ring-purple-200 cursor-pointer"
                  >
                    {tag}
                  </button>
                ))}

                {/* 自定义公司标签输入框或按钮 */}
                {customCompanyTagInputs[item.id]?.isEditing ? (
                  <div className="relative flex">
                    <Input
                      type="text"
                      placeholder="自定义"
                      value={customCompanyTagInputs[item.id]?.value || ''}
                      onChange={(e) => {
                        // 限制输入长度为20个字符
                        if (e.target.value.length <= 20) {
                          handleCustomCompanyTagInput(item.id, e.target.value);
                        }
                      }}
                      className="pl-3 pr-3 py-1 text-base md:text-base rounded-l-md border border-primary bg-white focus:ring-2 focus:ring-purple-200 focus:outline-none w-[100px] h-[26px] min-h-[26px] leading-tight"
                      onKeyDown={(e) => {
                        if (e.key === 'Enter') {
                          e.preventDefault();
                          const customTag = customCompanyTagInputs[item.id]?.value.trim();

                          if (customTag && customTag.length > 0) {
                            // 检查标签是否已存在
                            const tagExists = (item.company_tags || []).includes(customTag);

                            if (!tagExists) {
                              // 直接更新工作项目的标签
                              const updatedItems = [...workItems];
                              const itemIndex = updatedItems.findIndex(work => work.id === item.id);

                              if (itemIndex !== -1) {
                                const newTags = [...(updatedItems[itemIndex].company_tags || []), customTag];

                                updatedItems[itemIndex] = {
                                  ...updatedItems[itemIndex],
                                  company_tags: newTags
                                };

                                // 强制更新状态
                                setWorkItems([...updatedItems]);
                              }
                            }
                          }

                          // 重置输入框状态
                          setCustomCompanyTagInputs(prev => ({
                            ...prev,
                            [item.id]: { isEditing: false, value: '' }
                          }));
                        } else if (e.key === 'Escape') {
                          setCustomCompanyTagInputs(prev => ({
                            ...prev,
                            [item.id]: { isEditing: false, value: '' }
                          }));
                        }
                      }}
                      autoFocus
                    />

                    <button
                      type="button"
                      className="px-2 py-1 text-base rounded-r-md border border-l-0 border-primary bg-primary text-white hover:bg-purple-600 transition-colors h-[26px] min-h-[26px] leading-tight cursor-pointer"
                      onClick={(e) => {
                        e.preventDefault(); // 防止表单提交
                        e.stopPropagation(); // 防止事件冒泡

                        const customTag = customCompanyTagInputs[item.id]?.value.trim();

                        if (customTag && customTag.length > 0) {
                          // 检查标签是否已存在
                          const tagExists = (item.company_tags || []).includes(customTag);

                          if (!tagExists) {
                            // 直接更新工作项目的标签
                            const updatedItems = [...workItems];
                            const itemIndex = updatedItems.findIndex(work => work.id === item.id);

                            if (itemIndex !== -1) {
                              const newTags = [...(updatedItems[itemIndex].company_tags || []), customTag];

                              updatedItems[itemIndex] = {
                                ...updatedItems[itemIndex],
                                company_tags: newTags
                              };

                              // 强制更新状态
                              setWorkItems([...updatedItems]);
                            }
                          }

                          // 重置输入框状态
                          setCustomCompanyTagInputs(prev => ({
                            ...prev,
                            [item.id]: { isEditing: false, value: '' }
                          }));
                        }
                      }}
                    >
                      确定
                    </button>
                  </div>
                ) : (
                  <button
                    type="button"
                    className="flex items-center gap-2 px-3 py-1 text-base rounded-md border border-gray-200 bg-white text-gray-700 hover:bg-gray-50 transition-colors cursor-pointer"
                    onClick={() => showCustomCompanyTagInput(item.id)}
                  >
                    <svg xmlns="http://www.w3.org/2000/svg" width="10" height="10" viewBox="0 0 32 32" fill="none">
                      <path fillRule="evenodd" clipRule="evenodd" d="M14.3999 17.6V32H17.5999V17.6H32V14.4H17.5999V0H14.3999V14.4H0V17.6H14.3999Z" fill="#A1A3C4"></path>
                    </svg>
                    <span>自定义</span>
                  </button>
                )}
              </div>
            </div>

            {/* 职位名称 */}
            <div className="space-y-2">
              <Label htmlFor={`job_${item.id}`} className="text-base">职位名称</Label>
              <Input
                id={`job_${item.id}`}
                type="text"
                placeholder="如：产品经理"
                value={item.job}
                onChange={(e) => {
                  const updatedItems = workItems.map(work =>
                    work.id === item.id ? {...work, job: e.target.value} : work
                  );
                  setWorkItems(updatedItems);
                }}
                className="bg-white text-base md:text-base"
              />
              <div className="flex flex-wrap gap-2 mt-2">
                {/* 渲染所有职位标签 */}
                {(item.job_tags || []).map((tag) => (
                  <button
                    key={tag}
                    type="button"
                    onClick={() => {
                      // 移除标签
                      const updatedTags = (item.job_tags || []).filter(t => t !== tag);
                      const updatedItems = workItems.map(work =>
                        work.id === item.id ? {...work, job_tags: updatedTags} : work
                      );
                      setWorkItems(updatedItems);
                    }}
                    className="px-3 py-1 text-base rounded-md border border-primary bg-primary text-white transition-colors focus:outline-none focus:ring-2 focus:ring-purple-200 cursor-pointer"
                  >
                    {tag}
                  </button>
                ))}

                {/* 渲染未选中的预设职位标签 */}
                {['实习', '兼职'].filter(tag => !(item.job_tags || []).includes(tag)).map((tag) => (
                  <button
                    key={tag}
                    type="button"
                    onClick={() => {
                      // 添加标签
                      const updatedTags = [...(item.job_tags || []), tag];
                      const updatedItems = workItems.map(work =>
                        work.id === item.id ? {...work, job_tags: updatedTags} : work
                      );
                      setWorkItems(updatedItems);
                    }}
                    className="px-3 py-1 text-base rounded-md border border-gray-200 bg-white text-gray-700 hover:bg-gray-50 transition-colors focus:outline-none focus:ring-2 focus:ring-purple-200 cursor-pointer"
                  >
                    {tag}
                  </button>
                ))}

                {/* 自定义职位标签输入框或按钮 */}
                {customJobTagInputs[item.id]?.isEditing ? (
                  <div className="relative flex">
                    <Input
                      type="text"
                      placeholder="自定义"
                      value={customJobTagInputs[item.id]?.value || ''}
                      onChange={(e) => {
                        // 限制输入长度为20个字符
                        if (e.target.value.length <= 20) {
                          handleCustomJobTagInput(item.id, e.target.value);
                        }
                      }}
                      className="pl-3 pr-3 py-1 text-base md:text-base rounded-l-md border border-primary bg-white focus:ring-2 focus:ring-purple-200 focus:outline-none w-[100px] h-[26px] min-h-[26px] leading-tight"
                      onKeyDown={(e) => {
                        if (e.key === 'Enter') {
                          e.preventDefault();
                          const customTag = customJobTagInputs[item.id]?.value.trim();

                          if (customTag && customTag.length > 0) {
                            // 检查标签是否已存在
                            const tagExists = (item.job_tags || []).includes(customTag);

                            if (!tagExists) {
                              // 直接更新工作项目的标签
                              const updatedItems = [...workItems];
                              const itemIndex = updatedItems.findIndex(work => work.id === item.id);

                              if (itemIndex !== -1) {
                                const newTags = [...(updatedItems[itemIndex].job_tags || []), customTag];

                                updatedItems[itemIndex] = {
                                  ...updatedItems[itemIndex],
                                  job_tags: newTags
                                };

                                // 强制更新状态
                                setWorkItems([...updatedItems]);
                              }
                            }
                          }

                          // 重置输入框状态
                          setCustomJobTagInputs(prev => ({
                            ...prev,
                            [item.id]: { isEditing: false, value: '' }
                          }));
                        } else if (e.key === 'Escape') {
                          setCustomJobTagInputs(prev => ({
                            ...prev,
                            [item.id]: { isEditing: false, value: '' }
                          }));
                        }
                      }}
                      autoFocus
                    />

                    <button
                      type="button"
                      className="px-2 py-1 text-base rounded-r-md border border-l-0 border-primary bg-primary text-white hover:bg-purple-600 transition-colors h-[26px] min-h-[26px] leading-tight cursor-pointer"
                      onClick={(e) => {
                        e.preventDefault(); // 防止表单提交
                        e.stopPropagation(); // 防止事件冒泡

                        const customTag = customJobTagInputs[item.id]?.value.trim();

                        if (customTag && customTag.length > 0) {
                          // 检查标签是否已存在
                          const tagExists = (item.job_tags || []).includes(customTag);

                          if (!tagExists) {
                            // 直接更新工作项目的标签
                            const updatedItems = [...workItems];
                            const itemIndex = updatedItems.findIndex(work => work.id === item.id);

                            if (itemIndex !== -1) {
                              const newTags = [...(updatedItems[itemIndex].job_tags || []), customTag];

                              updatedItems[itemIndex] = {
                                ...updatedItems[itemIndex],
                                job_tags: newTags
                              };

                              // 强制更新状态
                              setWorkItems([...updatedItems]);
                            }
                          }

                          // 重置输入框状态
                          setCustomJobTagInputs(prev => ({
                            ...prev,
                            [item.id]: { isEditing: false, value: '' }
                          }));
                        }
                      }}
                    >
                      确定
                    </button>
                  </div>
                ) : (
                  <button
                    type="button"
                    className="flex items-center gap-2 px-3 py-1 text-base rounded-md border border-gray-200 bg-white text-gray-700 hover:bg-gray-50 transition-colors cursor-pointer"
                    onClick={() => showCustomJobTagInput(item.id)}
                  >
                    <svg xmlns="http://www.w3.org/2000/svg" width="10" height="10" viewBox="0 0 32 32" fill="none">
                      <path fillRule="evenodd" clipRule="evenodd" d="M14.3999 17.6V32H17.5999V17.6H32V14.4H17.5999V0H14.3999V14.4H0V17.6H14.3999Z" fill="#A1A3C4"></path>
                    </svg>
                    <span>自定义</span>
                  </button>
                )}
              </div>
            </div>

            {/* 所在部门 */}
            <div className="space-y-2">
              <Label htmlFor={`department_${item.id}`} className="text-base">所在部门</Label>
              <Input
                id={`department_${item.id}`}
                type="text"
                placeholder="如：产品部"
                value={item.department}
                onChange={(e) => {
                  const updatedItems = workItems.map(work =>
                    work.id === item.id ? {...work, department: e.target.value} : work
                  );
                  setWorkItems(updatedItems);
                }}
                className="bg-white text-base md:text-base"
              />
            </div>

            {/* 所在城市 */}
            <div className="space-y-2">
              <Label htmlFor={`city_${item.id}`} className="text-base">所在城市</Label>
              <Input
                id={`city_${item.id}`}
                type="text"
                placeholder="请输入所在城市"
                value={item.city}
                onChange={(e) => {
                  const updatedItems = workItems.map(work =>
                    work.id === item.id ? {...work, city: e.target.value} : work
                  );
                  setWorkItems(updatedItems);
                }}
                className="bg-white text-base md:text-base"
              />
            </div>

            {/* 开始时间 - 使用年月选择器 */}
            <YearMonthPicker
              id={`start_month_${item.id}`}
              label="开始时间"
              placeholder={`如：${new Date().getFullYear() - 3}-08`}
              value={item.start_month}
              onChange={(value) => {
                const updatedItems = workItems.map(work =>
                  work.id === item.id ? {...work, start_month: value} : work
                );
                setWorkItems(updatedItems);
              }}
            />

            {/* 结束时间 - 使用年月选择器 */}
            <YearMonthPicker
              id={`end_month_${item.id}`}
              label="结束时间"
              placeholder={`如：${new Date().getFullYear() - 1}-09，或选择'至今'`}
              value={item.end_month}
              onChange={(value) => {
                const updatedItems = workItems.map(work =>
                  work.id === item.id ? {...work, end_month: value} : work
                );
                setWorkItems(updatedItems);
              }}
            />

            {/* 工作描述 */}
            <div className="col-span-2">
              <div className="space-y-2">
                <Label htmlFor={`desc_${item.id}`} className="text-base">工作描述</Label>
                <MarkdownEditor
                  value={item.desc}
                  onChange={(value) => {
                    const updatedItems = workItems.map(work =>
                      work.id === item.id ? {...work, desc: value} : work
                    );
                    setWorkItems(updatedItems);
                  }}
                  height={200}
                  placeholder="填写示例:

1. 负责XX产品的XX功能开发，优化了XX流程，提升了XX效率
2. 参与XX项目的需求分析和方案设计，解决了XX问题
3. 通过XX手段，将XX指标提升了XX%，获得了XX奖励"
                />
              </div>
              <AIGenerateButtons
                className="w-full"
                markdownContent={item.desc}
                workInfo={{
                  company: item.company,
                  job: item.job,
                  department: item.department,
                  start_month: item.start_month,
                  end_month: item.end_month
                }}
                onContentGenerated={(content) => {
                  const updatedItems = workItems.map(work =>
                    work.id === item.id ? {...work, desc: content} : work
                  );
                  setWorkItems(updatedItems);
                }}
              />
            </div>
          </form>
        </div>
      )}
    </div>
  );
};

export default function Work() {
  // 从store中获取work模块数据
  const { modules, updateModuleItem, activeIndex, setActiveIndex } = useModuleStore();
  const workModule = modules['work'];

  // 使用ref来跟踪是否是本地状态更新导致的变化
  const isLocalUpdate = React.useRef(false);
  // 使用ref来跟踪是否是首次渲染
  const isFirstRender = React.useRef(true);

  // 从store数据转换为本地表单数据
  const convertStoreToLocal = (storeData: StoreWorkItem[]): WorkItem[] => {
    return storeData.map(item => ({
      id: item.id,
      company: item.company?.value || '',
      department: item.department?.value || '',
      city: item.city?.value || '',
      job: item.job?.value || '',
      start_month: item.start_month?.value || '',
      end_month: item.end_month?.value || '',
      desc: item.desc?.value || '',
      company_tags: item.company_tags?.value || [],
      job_tags: item.job_tags?.value || [],
      index: item.index
    }));
  };

  // 从本地表单数据转换为store数据
  const convertLocalToStore = (localData: WorkItem[]): StoreWorkItem[] => {
    return localData.map(item => ({
      id: item.id,
      company: { label: "公司名称", value: item.company },
      department: { label: "部门", value: item.department },
      city: { label: "城市", value: item.city },
      job: { label: "职位", value: item.job },
      start_month: { label: "开始时间", value: item.start_month },
      end_month: { label: "结束时间", value: item.end_month },
      desc: { label: "工作描述", value: item.desc },
      company_tags: { label: "公司标签", value: item.company_tags },
      job_tags: { label: "职位标签", value: item.job_tags },
      index: item.index
    }));
  };

  // 使用store中的数据，如果没有则使用空数组
  const [workItems, setWorkItems] = useState<WorkItem[]>(() => {
    if (workModule?.item && Array.isArray(workModule.item) && workModule.item.length > 0) {
      return convertStoreToLocal(workModule.item as StoreWorkItem[]);
    }
    return [];
  });

  // 初始化：如果store中没有数据，则创建一个默认的空项目
  useEffect(() => {
    if (workModule && (!workModule.item || (Array.isArray(workModule.item) && workModule.item.length === 0))) {
      // 创建一个默认的空工作经历项目
      const defaultWorkItem: WorkItem = {
        id: 'work-1',
        company: '',
        department: '',
        city: '',
        job: '',
        start_month: '',
        end_month: '',
        desc: '',
        company_tags: [],
        job_tags: [],
        index: 0
      };

      // 标记为本地更新
      isLocalUpdate.current = true;
      // 更新本地状态
      setWorkItems([defaultWorkItem]);
      // 更新store中的数据
      updateModuleItem('work', convertLocalToStore([defaultWorkItem]));
      // 重置标记
      setTimeout(() => {
        isLocalUpdate.current = false;
      }, 0);
    }
  }, [workModule, updateModuleItem]);

  // 当store中的数据变化时，更新本地状态，但仅当不是由本地更新触发的
  useEffect(() => {
    if (!isLocalUpdate.current && workModule?.item && Array.isArray(workModule.item) && workModule.item.length > 0) {
      setWorkItems(convertStoreToLocal(workModule.item as StoreWorkItem[]));
    }
  }, [workModule?.item]);

  // 当本地状态变化时，更新store，但需要防止无限循环
  useEffect(() => {
    // 如果是首次渲染，标记为非首次渲染并返回
    if (isFirstRender.current) {
      isFirstRender.current = false;
      return;
    }

    // 仅当不是从store更新本地状态时才更新store
    if (workModule && !isLocalUpdate.current) {
      // 标记为本地更新
      isLocalUpdate.current = true;
      // 将本地数据转换为store格式并更新
      updateModuleItem('work', convertLocalToStore(workItems));
      // 重置标记
      setTimeout(() => {
        isLocalUpdate.current = false;
      }, 0);
    }
  }, [workItems, workModule, updateModuleItem]);

  // 展开/折叠状态 - 使用对象来跟踪每个工作经历项目的展开状态
  const [expandedItems, setExpandedItems] = useState<Record<string, boolean>>(() => {
    // 根据 activeIndex 数组确定默认展开的项目
    if (activeIndex.length > 0 && workItems.length > 0) {
      const initialExpanded: Record<string, boolean> = {};
      activeIndex.forEach(index => {
        if (workItems[index]) {
          initialExpanded[workItems[index].id] = true;
        }
      });
      return initialExpanded;
    }
    // 如果没有 activeIndex 或对应项目不存在，默认展开第一个项目
    return workItems.length > 0 ? { [workItems[0].id]: true } : {};
  });

  // 监听 activeIndex 变化，更新展开状态
  useEffect(() => {
    if (activeIndex.length > 0 && workItems.length > 0) {
      // 根据 activeIndex 数组展开对应的项目
      const newExpandedItems: Record<string, boolean> = {};
      activeIndex.forEach(index => {
        if (workItems[index]) {
          newExpandedItems[workItems[index].id] = true;
        }
      });
      setExpandedItems(newExpandedItems);
    }
  }, [activeIndex, workItems]);

  // 自定义公司标签输入状态
  const [customCompanyTagInputs, setCustomCompanyTagInputs] = useState<Record<string, {isEditing: boolean, value: string}>>({});

  // 自定义职位标签输入状态
  const [customJobTagInputs, setCustomJobTagInputs] = useState<Record<string, {isEditing: boolean, value: string}>>({});

  // 拖拽状态
  const [activeId, setActiveId] = useState<string | null>(null);

  // 删除确认对话框状态
  const [deleteConfirmOpen, setDeleteConfirmOpen] = useState(false);
  const [itemToDelete, setItemToDelete] = useState<{id: string, name: string} | null>(null);

  // 配置传感器 - 在DndContext中使用
  // 注意：这个变量在下面的DndContext组件中使用

  // 切换展开/折叠状态
  const toggleExpanded = (itemId: string) => {
    const itemIndex = workItems.findIndex(item => item.id === itemId);
    if (itemIndex === -1) return;

    // 先更新展开状态
    setExpandedItems(prev => {
      const newExpanded = {
        ...prev,
        [itemId]: !prev[itemId]
      };

      // 在下一个事件循环中更新 activeIndex，避免在渲染过程中调用 setState
      setTimeout(() => {
        const currentActiveIndex = [...activeIndex];
        if (newExpanded[itemId]) {
          // 展开：添加到 activeIndex
          if (!currentActiveIndex.includes(itemIndex)) {
            currentActiveIndex.push(itemIndex);
            setActiveIndex(currentActiveIndex);
          }
        } else {
          // 折叠：从 activeIndex 移除
          const indexToRemove = currentActiveIndex.indexOf(itemIndex);
          if (indexToRemove > -1) {
            currentActiveIndex.splice(indexToRemove, 1);
            setActiveIndex(currentActiveIndex);
          }
        }
      }, 0);

      return newExpanded;
    });
  };

  // 处理拖拽开始
  const handleDragStart = (event: DragStartEvent) => {
    setActiveId(event.active.id as string);
  };

  // 处理拖拽结束
  const handleDragEnd = (event: DragEndEvent) => {
    const { active, over } = event;

    if (over && active.id !== over.id) {
      // 获取排序后的工作经历数组
      const oldIndex = workItems.findIndex(item => item.id === active.id);
      const newIndex = workItems.findIndex(item => item.id === over.id);

      // 更新工作经历项目的顺序
      const updatedItems = [...workItems];
      const [movedItem] = updatedItems.splice(oldIndex, 1);
      updatedItems.splice(newIndex, 0, movedItem);

      // 更新所有项目的索引
      const reindexedItems = updatedItems.map((item, index) => ({
        ...item,
        index
      }));

      // 更新状态
      setWorkItems(reindexedItems);
    }

    // 重置拖拽状态
    setActiveId(null);
  };

  // 添加新的工作经历
  const addWorkItem = () => {
    // 生成唯一ID
    const newId = `work-${Date.now()}`;

    // 创建新的工作经历项目
    const newWorkItem: WorkItem = {
      id: newId,
      company: '',
      department: '',
      city: '',
      job: '',
      start_month: '',
      end_month: '',
      desc: '',
      company_tags: [],
      job_tags: [],
      index: workItems.length // 设置索引为当前列表长度
    };

    // 添加到列表中
    setWorkItems([...workItems, newWorkItem]);

    // 自动展开新添加的项目
    setExpandedItems(prev => ({
      ...prev,
      [newId]: true
    }));
  };

  // 显示删除确认对话框
  const showDeleteConfirm = (itemId: string) => {
    // 如果只剩下一个项目，不允许删除
    if (workItems.length <= 1) {
      return;
    }

    const item = workItems.find(item => item.id === itemId);
    if (item) {
      setItemToDelete({
        id: itemId,
        name: item.company || '工作经历'
      });
      setDeleteConfirmOpen(true);
    }
  };

  // 确认删除工作经历项目
  const confirmDeleteWorkItem = () => {
    if (!itemToDelete) return;

    // 从列表中移除项目
    const updatedItems = workItems.filter(item => item.id !== itemToDelete.id);

    // 更新状态
    setWorkItems(updatedItems);

    // 从展开状态中移除该项目
    const newExpandedItems = { ...expandedItems };
    delete newExpandedItems[itemToDelete.id];
    setExpandedItems(newExpandedItems);

    // 重置删除状态
    setItemToDelete(null);
  };

  // 保留原有的删除函数以兼容现有代码
  const deleteWorkItem = (itemId: string) => {
    showDeleteConfirm(itemId);
  };

  // 显示自定义公司标签输入框
  const showCustomCompanyTagInput = (itemId: string) => {
    setCustomCompanyTagInputs(prev => ({
      ...prev,
      [itemId]: { isEditing: true, value: '' }
    }));
  };

  // 处理自定义公司标签输入
  const handleCustomCompanyTagInput = (itemId: string, value: string) => {
    setCustomCompanyTagInputs(prev => ({
      ...prev,
      [itemId]: { ...prev[itemId], value }
    }));
  };

  // 显示自定义职位标签输入框
  const showCustomJobTagInput = (itemId: string) => {
    setCustomJobTagInputs(prev => ({
      ...prev,
      [itemId]: { isEditing: true, value: '' }
    }));
  };

  // 处理自定义职位标签输入
  const handleCustomJobTagInput = (itemId: string, value: string) => {
    setCustomJobTagInputs(prev => ({
      ...prev,
      [itemId]: { ...prev[itemId], value }
    }));
  };

  // 定义提示项
  const tipItems = [
    {
      question: '写好工作经历是不是一定要吹牛？',
      answer: '我基本赞同这个观点\n很多公司都是面试造火箭，实际打螺丝，\n当一个岗位只有几千块薪水，但是要求却一大堆时，这就是一个信号——你可以开始吹牛了，吹的不好你就不能进来打螺丝。'
    },
    {
      question: '吹牛等于造假吗？',
      answer: '不等于，有些吹牛甚至可以让受众如沐春风。\n比如：\n我的工作是送文件，但是我可以写成：协调部门工作，上传下达，保证公司高效运转。\n我的工作是发问卷，但是我可以写成：参与市场调查，接触xx人以上，了解用户真实需求，为公司决策提供数据支持。'
    },
    {
      question: '怎么写好工作经历？',
      answer: '万能公式：动作/手段+结果\n通过 XX 提升 XX 效率XX% ，将销售额提高XX%；\n通过数据分析，提出XX解决方案，成功将XX从XX提升至XX；\n如果外行人无法看出有多么厉害，加上对比解释，如：比去年同期提高xx%，较同期入职人员高出xx倍。'
    },
    {
      question: '写出来太像流水账怎么办？',
      answer: '点击附近的套用案例或者使用我们预设好的AI助手帮你搞定，\n记得套用或者生成完要修改一下显得更真实哦。'
    },
    {
      question: '如何量化自己的工作数据？',
      answer: '用数据表现出来结果会更可信，更专业。但是不要乱用数据，面试时可能会被问倒。\n可以翻一翻自己的工作记录，做一下简单预估；\n如：\n小编2天出1篇文章，简单估算1周3篇，1个月12篇....\n做运营，1天接触用户30个，1周5天就是150个....\n做自媒体，平均1篇文章阅读量2000，10篇文章就是20000......'
    },
    {
      question: '一段经历时间太短怎么写？',
      answer: '除非是实习经历,否则一般少于3个月的工作经历都不建议写在简历上。'
    },
    {
      question: '同一段经历升过职/转过岗怎么写？',
      answer: '记住两条：\n写较高的职位；\n写与你现在求职意向更相关的职位；'
    },
    {
      question: '工作经历与申请的职位无关要写吗？',
      answer: '匹配企业的能力要求是核心，如果这个经历能够体现与企业匹配的能力就可以写。\n不匹配的建议不要写。'
    },
    {
      question: '实习经历要写吗？',
      answer: '正式工作经历丰富的建议不写实习经历，篇幅太长太啰嗦会影响对方看到你的重点内容。'
    }
  ];

  return (
    <div className="h-full">
      <div className="flex gap-4 h-full">
        <div className="w-48">
          <Tip
            title="工作经历怎么写？"
            description="还有疑惑? 立即反馈"
            tipContent={
              <>
                填写简历的核心思路就是——扬长避短，突出自己和岗位的匹配度。面试或者简历上有缺点没关系，反而更真实，但不要暴露硬伤。
              </>
            }
            items={tipItems}
          />
        </div>

        <div className="flex-1 rounded-md flex flex-col bg-white">
          {/* 使用 ScrollableContent 组件 */}
          <ScrollableContent>
            {/* 模块头部 */}
            <ModuleHeader
              id="work"
              icon={<Image src="/image/work.svg" alt="工作经历" width={24} height={24} />}
              title="工作经历"
              description="此处可填工作和实习的经历，在经历丰富的情况下尽量挑选与目标职位要求或者业务相关的🎯"
            />

            {/* 表单内容区域 */}
            <div className="p-6 space-y-6 bg-[#f7f8fa] rounded-md">
              {/* 工作经历列表 */}
              <div className="flex-1">
                {/* 使用DndContext包装工作经历列表 */}
                <DndContext
                  sensors={useSensors(
                    useSensor(PointerSensor, {
                      activationConstraint: {
                        distance: 2, // 只需要拖动2px就会触发拖拽
                        tolerance: 3, // 增加容差，使拖拽更容易触发
                        delay: 0, // 无延迟
                      },
                    }),
                    useSensor(KeyboardSensor, {
                      coordinateGetter: sortableKeyboardCoordinates,
                    })
                  )}
                  collisionDetection={closestCenter}
                  onDragStart={handleDragStart}
                  onDragEnd={handleDragEnd}
                  measuring={{
                    droppable: {
                      strategy: MeasuringStrategy.Always,
                    },
                  }}
                >
                  <SortableContext
                    items={Array.isArray(workItems) ? workItems.map(item => item.id) : []}
                    strategy={verticalListSortingStrategy}
                  >
                    {Array.isArray(workItems) && workItems.map((item) => (
                      <SortableWorkItem
                        key={item.id}
                        item={item}
                        expandedItems={expandedItems}
                        toggleExpanded={toggleExpanded}
                        deleteWorkItem={deleteWorkItem}
                        workItems={workItems}
                        setWorkItems={setWorkItems}
                        customCompanyTagInputs={customCompanyTagInputs}
                        handleCustomCompanyTagInput={handleCustomCompanyTagInput}
                        showCustomCompanyTagInput={showCustomCompanyTagInput}
                        setCustomCompanyTagInputs={setCustomCompanyTagInputs}
                        customJobTagInputs={customJobTagInputs}
                        handleCustomJobTagInput={handleCustomJobTagInput}
                        showCustomJobTagInput={showCustomJobTagInput}
                        setCustomJobTagInputs={setCustomJobTagInputs}
                      />
                    ))}
                  </SortableContext>

                  {/* 拖拽预览 */}
                  <DragOverlay>
                    {activeId && Array.isArray(workItems) ? (
                      <div className="flex items-center justify-between w-full text-base text-[#2E2F66] bg-white p-4 rounded-md shadow-lg border-2 border-purple-200">
                        <div className="flex items-center gap-3">
                          {(() => {
                            const activeItem = workItems.find(item => item.id === activeId);
                            return (
                              <>
                                <div className="w-[137px] text-ellipsis overflow-hidden whitespace-nowrap" title={activeItem?.company}>
                                  {activeItem?.company || '未填写公司名称'}
                                </div>
                                <div>
                                  {activeItem?.start_month || '未填写'} <span>- {activeItem?.end_month || '未填写'}</span>
                                </div>
                                <div className="flex gap-5 items-center">
                                  <div className={cn(
                                    "cursor-grab p-1 rounded-md bg-gray-100 text-primary"
                                  )}>
                                    <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 20 20" fill="none">
                                      <path fillRule="evenodd" clipRule="evenodd" d="M7.19958 15.0532C7.47572 15.0532 7.69958 14.8294 7.69958 14.5532L7.69957 5.44624C7.69957 5.23032 7.56097 5.03879 7.35587 4.9713C7.15077 4.9038 6.92551 4.97559 6.79728 5.14932L4.55673 8.18498C4.39274 8.40716 4.43992 8.72021 4.66209 8.88419C4.88427 9.04818 5.19732 9.001 5.36131 8.77882L6.69958 6.96565L6.69958 14.5532C6.69958 14.8294 6.92343 15.0532 7.19958 15.0532Z" fill="currentColor"></path>
                                      <path fillRule="evenodd" clipRule="evenodd" d="M12.8004 4.94678C12.5243 4.94678 12.3004 5.17063 12.3004 5.44678L12.3004 14.5538C12.3004 14.7697 12.439 14.9612 12.6441 15.0287C12.8492 15.0962 13.0745 15.0244 13.2027 14.8507L15.4433 11.815C15.6073 11.5928 15.5601 11.2798 15.3379 11.1158C15.1157 10.9518 14.8027 10.999 14.6387 11.2212L13.3004 13.0344L13.3004 5.44678C13.3004 5.17063 13.0766 4.94678 12.8004 4.94678Z" fill="currentColor"></path>
                                    </svg>
                                  </div>
                                </div>
                              </>
                            );
                          })()}
                        </div>
                      </div>
                    ) : null}
                  </DragOverlay>
                </DndContext>
              </div>
            </div>

            {/* 添加工作经历按钮和跳转器 */}
            <div className="mt-6 flex justify-between items-center">
              <button
                type="button"
                onClick={addWorkItem}
                className="flex items-center justify-center gap-2 py-2 px-5 rounded-md transition-all bg-white text-[#333] border border-gray-200 hover:bg-gray-50 active:bg-gray-100 focus:outline-none focus:ring-2 focus:ring-gray-200 cursor-pointer shadow-sm whitespace-nowrap"
              >
                <svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
                  <path d="M8 4V12" stroke="#333" strokeWidth="1.5" strokeLinecap="round"/>
                  <path d="M4 8H12" stroke="#333" strokeWidth="1.5" strokeLinecap="round"/>
                </svg>
                <span className="text-base font-medium">工作经历</span>
              </button>

              <ModuleNavigator currentModuleId="work" />
            </div>
          </ScrollableContent>
        </div>
      </div>

      {/* 删除确认对话框 */}
      <ItemDeleteConfirmDialog
        open={deleteConfirmOpen}
        onOpenChange={setDeleteConfirmOpen}
        itemName={itemToDelete?.name || ''}
        itemType="工作经历"
        onConfirm={confirmDeleteWorkItem}
        disabled={workItems.length <= 1}
        disabledReason="至少需要保留一个工作经历"
      />
    </div>
  );
}
