'use client';

import React, { useState, useRef } from 'react';
import Tip from '../Tip';
import ModuleNavigator from '@/components/ModuleNavigator';
import ScrollableContent from '../ScrollableContent';
import ModuleHeader from '@/components/ModuleHeader';
import Image from 'next/image';
import { useModuleStore, type PortfolioItem as StorePortfolioItem } from '@/store/useModuleStore';
import QRCode from 'react-qr-code';
import { cn } from '@/lib/utils';
import { uploadApi } from '@/api/client/upload';
import { toast } from 'sonner';
import {
  DndContext,
  closestCenter,
  KeyboardSensor,
  PointerSensor,
  useSensor,
  useSensors,
  DragEndEvent,
  DragStartEvent,
  DragOverlay,
  MeasuringStrategy,
} from '@dnd-kit/core';
import {
  SortableContext,
  sortableKeyboardCoordinates,
  useSortable,
  verticalListSortingStrategy,
} from '@dnd-kit/sortable';
import { CSS } from '@dnd-kit/utilities';
import ItemDeleteConfirmDialog from '../ItemDeleteConfirmDialog';

// 本地作品项类型
interface LocalPortfolioItem {
  id: string;
  name: string;
  url: string;
  index: number;
}

// 可排序的作品项组件
const SortablePortfolioItem = ({
  item,
  deletePortfolioItem,
  portfolioItems,
  editingId,
  setEditingId,
  updatePortfolioItem
}: {
  item: LocalPortfolioItem;
  deletePortfolioItem: (id: string) => void;
  portfolioItems: LocalPortfolioItem[];
  editingId: string | null;
  setEditingId: (id: string | null) => void;
  updatePortfolioItem: (id: string, name: string) => void;
}) => {
  // 编辑状态下的作品名称
  const [editName, setEditName] = React.useState(item.name);

  // 处理输入框变化
  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setEditName(e.target.value);
  };

  // 处理输入框失焦或按下回车键
  const handleInputBlur = () => {
    if (editName.trim()) {
      updatePortfolioItem(item.id, editName.trim());
    }
    setEditingId(null);
  };

  // 处理按键事件
  const handleKeyDown = (e: React.KeyboardEvent<HTMLInputElement>) => {
    if (e.key === 'Enter') {
      handleInputBlur();
    } else if (e.key === 'Escape') {
      setEditName(item.name); // 恢复原始名称
      setEditingId(null);
    }
  };
  const {
    attributes,
    listeners,
    setNodeRef,
    transform,
    transition,
    isDragging,
  } = useSortable({ id: item.id });

  const style = {
    transform: CSS.Transform.toString(transform),
    transition,
    opacity: isDragging ? 0.5 : 1,
    zIndex: isDragging ? 999 : 'auto',
    position: 'relative' as const,
    touchAction: 'none',
  };

  return (
    <div ref={setNodeRef} style={style} className="mb-3">
      <div className="flex items-center justify-between w-full text-base text-[#2E2F66] bg-[#f7f7fc] px-4 py-2 rounded-md cursor-pointer">
        <div className="flex items-center">
          <div className="bg-white p-2 rounded-md mr-3">
            <Image
              src="/image/file-icon.svg"
              alt="文件"
              width={20}
              height={20}
              onError={(e) => {
                // 如果图标加载失败，使用默认图标
                const target = e.target as HTMLImageElement;
                target.onerror = null;
                target.src = "/image/portfolio-upload.svg";
              }}
            />
          </div>
          {editingId === item.id ? (
            <input
              type="text"
              className="text-base md:text-base bg-white border border-gray-300 rounded px-2 py-1 focus:outline-none focus:border-primary"
              value={editName}
              onChange={handleInputChange}
              onBlur={handleInputBlur}
              onKeyDown={handleKeyDown}
              autoFocus
            />
          ) : (
            <span className="text-base">{item.name}</span>
          )}
        </div>
        <div className="flex gap-5 items-center">
          <div
            className={cn(
              "cursor-grab active:cursor-grabbing p-1 rounded-md transition-colors",
              "hover:bg-gray-100 hover:text-primary"
            )}
            {...listeners}
            {...attributes}
            title="拖拽排序"
          >
            <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 20 20" fill="none">
              <path fillRule="evenodd" clipRule="evenodd" d="M7.19958 15.0532C7.47572 15.0532 7.69958 14.8294 7.69958 14.5532L7.69957 5.44624C7.69957 5.23032 7.56097 5.03879 7.35587 4.9713C7.15077 4.9038 6.92551 4.97559 6.79728 5.14932L4.55673 8.18498C4.39274 8.40716 4.43992 8.72021 4.66209 8.88419C4.88427 9.04818 5.19732 9.001 5.36131 8.77882L6.69958 6.96565L6.69958 14.5532C6.69958 14.8294 6.92343 15.0532 7.19958 15.0532Z" fill="currentColor"></path>
              <path fillRule="evenodd" clipRule="evenodd" d="M12.8004 4.94678C12.5243 4.94678 12.3004 5.17063 12.3004 5.44678L12.3004 14.5538C12.3004 14.7697 12.439 14.9612 12.6441 15.0287C12.8492 15.0962 13.0745 15.0244 13.2027 14.8507L15.4433 11.815C15.6073 11.5928 15.5601 11.2798 15.3379 11.1158C15.1157 10.9518 14.8027 10.999 14.6387 11.2212L13.3004 13.0344L13.3004 5.44678C13.3004 5.17063 13.0766 4.94678 12.8004 4.94678Z" fill="currentColor"></path>
            </svg>
          </div>
          <div
            className="cursor-pointer hover:text-primary"
            onClick={() => setEditingId(item.id)}
            title="编辑作品名称"
          >
            <Image src="/assets/svg/edit-icon.svg" alt="编辑" width={20} height={20} />
          </div>
          <div
            className="cursor-pointer hover:text-primary"
            onClick={() => window.open(item.url, '_blank')}
            title="查看作品"
            style={{ width: '20px', height: '20px', display: 'flex', alignItems: 'center', justifyContent: 'center' }}
          >
            <svg width="10" height="10" viewBox="0 0 10 10" fill="none" xmlns="http://www.w3.org/2000/svg">
              <path d="M4 1H9V6M9 1L1 9" stroke="currentColor" strokeWidth="1" strokeLinecap="round" strokeLinejoin="round"/>
            </svg>
          </div>
          <div
            className={`cursor-pointer ${portfolioItems.length <= 1 ? 'opacity-50' : 'hover:text-red-500'}`}
            onClick={() => {
              if (portfolioItems.length > 1) {
                deletePortfolioItem(item.id);
              }
            }}
            title={portfolioItems.length <= 1 ? "至少保留一个作品" : "删除此作品"}
          >
            <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 20 20" fill="none">
              <path fillRule="evenodd" clipRule="evenodd" d="M6.7251 8.77979C7.00124 8.77979 7.2251 9.00364 7.2251 9.27979V14.7341H12.7746V9.27979C12.7746 9.00364 12.9985 8.77979 13.2746 8.77979C13.5508 8.77979 13.7746 9.00364 13.7746 9.27979V15.2341C13.7746 15.5103 13.5508 15.7341 13.2746 15.7341H6.7251C6.44896 15.7341 6.2251 15.5103 6.2251 15.2341V9.27979C6.2251 9.00364 6.44896 8.77979 6.7251 8.77979Z" fill="currentColor"></path>
              <path fillRule="evenodd" clipRule="evenodd" d="M4.76807 6.78955C4.76807 6.51341 4.99192 6.28955 5.26807 6.28955L14.732 6.28955C15.0082 6.28955 15.232 6.51341 15.232 6.78955C15.232 7.06569 15.0082 7.28955 14.732 7.28955L5.26807 7.28955C4.99192 7.28955 4.76807 7.06569 4.76807 6.78955Z" fill="currentColor"></path>
              <path fillRule="evenodd" clipRule="evenodd" d="M7.76147 4.76611C7.76147 4.48997 7.98533 4.26611 8.26147 4.26611H11.7384C12.0145 4.26611 12.2384 4.48997 12.2384 4.76611C12.2384 5.04226 12.0145 5.26611 11.7384 5.26611H8.26147C7.98533 5.26611 7.76147 5.04226 7.76147 4.76611Z" fill="currentColor"></path>
              <path fillRule="evenodd" clipRule="evenodd" d="M8.91357 8.95068C9.18972 8.95068 9.41357 9.17454 9.41357 9.45068V13.1917C9.41357 13.4679 9.18972 13.6917 8.91357 13.6917C8.63743 13.6917 8.41357 13.4679 8.41357 13.1917V9.45068C8.41357 9.17454 8.63743 8.95068 8.91357 8.95068Z" fill="currentColor"></path>
              <path fillRule="evenodd" clipRule="evenodd" d="M11.0864 8.95068C11.3626 8.95068 11.5864 9.17454 11.5864 9.45068V13.1917C11.5864 13.4679 11.3626 13.6917 11.0864 13.6917C10.8103 13.6917 10.5864 13.4679 10.5864 13.1917V9.45068C10.5864 9.17454 10.8103 8.95068 11.0864 8.95068Z" fill="currentColor"></path>
            </svg>
          </div>
        </div>
      </div>
    </div>
  );
};

export default function Portfolio() {
  // 使用store获取作品集数据
  const { modules, updateModuleItem } = useModuleStore();
  const portfolioModule = modules['portfolio'];

  // 从store数据转换为本地表单数据
  const convertStoreToLocal = (storeData: StorePortfolioItem[]): LocalPortfolioItem[] => {
    return storeData.map(item => ({
      id: item.id,
      name: item.name?.value || '',
      url: item.url?.value || '',
      index: item.index
    }));
  };

  // 从本地表单数据转换为store数据
  const convertLocalToStore = (localData: LocalPortfolioItem[]): StorePortfolioItem[] => {
    return localData.map(item => ({
      id: item.id,
      name: { label: "作品名称", value: item.name },
      url: { label: "作品链接", value: item.url },
      index: item.index
    }));
  };

  // 初始化本地状态
  const [portfolioItems, setPortfolioItems] = useState<LocalPortfolioItem[]>(() => {
    if (portfolioModule?.item && Array.isArray(portfolioModule.item) && portfolioModule.item.length > 0) {
      return convertStoreToLocal(portfolioModule.item as StorePortfolioItem[]);
    }
    return [];
  });

  // 拖拽状态
  const [activeId, setActiveId] = useState<string | null>(null);

  // 编辑状态
  const [editingId, setEditingId] = useState<string | null>(null);

  // 删除确认对话框状态
  const [deleteConfirmOpen, setDeleteConfirmOpen] = useState(false);
  const [itemToDelete, setItemToDelete] = useState<{id: string, name: string} | null>(null);

  // 上传状态
  const [isUploading, setIsUploading] = useState(false);

  // 文件输入引用
  const fileInputRef = useRef<HTMLInputElement>(null);

  // 获取当前拖拽的项目
  const activeItem = activeId ? portfolioItems.find(item => item.id === activeId) : null;

  // 更新作品名称
  const updatePortfolioItem = (id: string, name: string) => {
    const updatedItems = portfolioItems.map(item =>
      item.id === id ? { ...item, name } : item
    );

    // 先更新本地状态
    setPortfolioItems(updatedItems);

    // 使用setTimeout将store更新放到下一个事件循环，避免在渲染过程中更新状态
    setTimeout(() => {
      updateModuleItem('portfolio', convertLocalToStore(updatedItems));
    }, 0);
  };

  // 监听portfolioModule的变化，更新本地状态
  React.useEffect(() => {
    if (portfolioModule?.item && Array.isArray(portfolioModule.item) && portfolioModule.item.length > 0) {
      setPortfolioItems(convertStoreToLocal(portfolioModule.item as StorePortfolioItem[]));
    }
  }, [portfolioModule]);

  // 设置传感器
  const sensors = useSensors(
    useSensor(PointerSensor, {
      activationConstraint: {
        distance: 2, // 只需要拖动2px就会触发拖拽
        tolerance: 3, // 增加容差，使拖拽更容易触发
        delay: 0, // 无延迟
      },
    }),
    useSensor(KeyboardSensor, {
      coordinateGetter: sortableKeyboardCoordinates,
    })
  );

  // 处理拖拽开始
  const handleDragStart = (event: DragStartEvent) => {
    const { active } = event;
    setActiveId(active.id as string);
  };

  // 处理拖拽结束
  const handleDragEnd = (event: DragEndEvent) => {
    const { active, over } = event;

    if (over && active.id !== over.id) {
      setPortfolioItems(items => {
        const oldIndex = items.findIndex(item => item.id === active.id);
        const newIndex = items.findIndex(item => item.id === over.id);

        // 创建新数组并重新排序
        const newItems = [...items];
        const [movedItem] = newItems.splice(oldIndex, 1);
        newItems.splice(newIndex, 0, movedItem);

        // 更新索引
        const updatedItems = newItems.map((item, index) => ({
          ...item,
          index
        }));

        // 使用setTimeout将store更新放到下一个事件循环，避免在渲染过程中更新状态
        setTimeout(() => {
          updateModuleItem('portfolio', convertLocalToStore(updatedItems));
        }, 0);

        return updatedItems;
      });
    }

    setActiveId(null);
  };

  // 定义提示项
  const tipItems = [
    {
      question: '我该放哪些作品？',
      answer: '选取最具代表性和对应岗位最相关的作品。'
    },
    {
      question: '不是独立完成的作品可以放吗？',
      answer: '可以放，作为团队的一员你也作品贡献了力量，在面试时阐述你的贡献和你所扮演的角色就可以了。'
    }
  ];

  // 显示删除确认对话框
  const showDeleteConfirm = (id: string) => {
    const item = portfolioItems.find(item => item.id === id);
    if (item) {
      setItemToDelete({
        id: id,
        name: item.name || '作品'
      });
      setDeleteConfirmOpen(true);
    }
  };

  // 确认删除作品
  const confirmDeletePortfolio = () => {
    if (!itemToDelete) return;

    const newItems = portfolioItems.filter(item => item.id !== itemToDelete.id);
    // 更新索引
    const updatedItems = newItems.map((item, index) => ({
      ...item,
      index
    }));

    // 先更新本地状态
    setPortfolioItems(updatedItems);

    // 使用setTimeout将store更新放到下一个事件循环，避免在渲染过程中更新状态
    setTimeout(() => {
      updateModuleItem('portfolio', convertLocalToStore(updatedItems));
    }, 0);

    // 重置删除状态
    setItemToDelete(null);
  };

  // 保留原有的删除函数以兼容现有代码
  const handleDeletePortfolio = (id: string) => {
    showDeleteConfirm(id);
  };

  // 处理文件选择
  const handleFileSelect = (e: React.ChangeEvent<HTMLInputElement>) => {
    const files = e.target.files;
    if (!files || files.length === 0) return;

    const file = files[0];
    handleFileUpload(file);

    // 重置文件输入，以便可以再次选择同一个文件
    if (fileInputRef.current) {
      fileInputRef.current.value = '';
    }
  };

  // 处理文件拖放
  const handleDrop = (e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault();
    e.stopPropagation();

    if (e.dataTransfer.files && e.dataTransfer.files.length > 0) {
      const file = e.dataTransfer.files[0];
      handleFileUpload(file);
    }
  };

  // 阻止默认拖放行为
  const handleDragOver = (e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault();
    e.stopPropagation();
  };

  // 处理文件上传
  const handleFileUpload = async (file: File) => {
    // 检查文件类型
    const allowedTypes = [
      'application/pdf',
      'application/msword',
      'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
      'image/jpeg',
      'image/png',
      'application/zip',
      'application/x-rar-compressed'
    ];

    if (!allowedTypes.includes(file.type)) {
      toast.error('不支持的文件类型，请上传PDF、DOC、DOCX、JPG、PNG、ZIP或RAR格式的文件', {
        position: 'top-center'
      });
      return;
    }

    // 检查文件大小（20MB = 20 * 1024 * 1024 字节）
    if (file.size > 20 * 1024 * 1024) {
      toast.error('文件大小不能超过20MB', {
        position: 'top-center'
      });
      return;
    }

    try {
      setIsUploading(true);

      // 调用上传API
      const result = await uploadApi.uploadAttachment(file);

      // 创建新的作品项
      const newItem = {
        id: `portfolio-${Date.now()}`,
        name: file.name,
        url: result.url,
        index: portfolioItems.length
      };

      // 更新作品列表
      const updatedItems = [...portfolioItems, newItem];

      // 更新本地状态
      setPortfolioItems(updatedItems);

      // 更新store
      setTimeout(() => {
        updateModuleItem('portfolio', convertLocalToStore(updatedItems));
      }, 0);

      toast.success('作品上传成功', {
        position: 'top-center'
      });
    } catch (error) {
      toast.error('作品上传失败，请重试', {
        position: 'top-center'
      });
    } finally {
      setIsUploading(false);
    }
  };

  return (
    <div className="h-full">
      <div className="flex gap-4 h-full">
        <div className="w-48">
          <Tip
            title="作品集怎么写？"
            description="还有疑惑? 立即反馈"
            tipContent={
              <>
                作品名称要让对方产生兴趣。神秘的、高深的、通俗的、直白的都可以。🖼️
              </>
            }
            items={tipItems}
          />
        </div>

        <div className="flex-1 rounded-md flex flex-col bg-white">
          {/* 使用 ScrollableContent 组件 */}
          <ScrollableContent>
            {/* 模块头部 */}
            <ModuleHeader
              id="portfolio"
              icon={<Image src="/image/portfolio.svg" alt="作品集" width={24} height={24} />}
              title="作品集"
              description="优秀的作品展示将非常直观地展现你之前的成果和实力💪"
            />

            {/* 表单内容区域 */}
            <div className="p-6 space-y-6">
              {/* 这里是作品集的主要内容区域 */}
              <h2 className="text-center text-base font-medium mb-4">可自动生成作品链接和作品二维码</h2>
              <div
                className={`flex-1 flex flex-col items-center justify-center min-h-[300px] border border-dashed border-gray-300 rounded-lg p-8 bg-[#f7f7fc] cursor-pointer hover:border-solid hover:border-[#6366f1] transition-all ${isUploading ? 'opacity-70 pointer-events-none' : ''}`}
                onDrop={handleDrop}
                onDragOver={handleDragOver}
                onClick={() => fileInputRef.current?.click()}
              >
                {/* 隐藏的文件输入 */}
                <input
                  type="file"
                  ref={fileInputRef}
                  onChange={handleFileSelect}
                  className="hidden"
                  accept=".pdf,.doc,.docx,.jpg,.jpeg,.png,.zip,.rar"
                />

                {/* 拖拽上传区域 */}
                <div className="flex flex-col items-center justify-center mb-1">
                  <div className="mb-2">
                    <Image
                      src="/image/portfolio-upload.svg"
                      alt="将作品拖拽至此处"
                      width={91}
                      height={73}
                      priority
                    />
                  </div>
                  <p className="text-base text-center mt-2">将作品拖拽至此处</p>
                </div>

                {/* 上传按钮 */}
                <button
                  className="bg-white text-[#6366f1] border border-[#6366f1] rounded-full py-1 px-8 mb-4 hover:bg-[#6366f1] hover:text-white transition-colors text-base cursor-pointer"
                  onClick={(e) => {
                    e.stopPropagation();
                    fileInputRef.current?.click();
                  }}
                  disabled={isUploading}
                >
                  {isUploading ? '上传中...' : '点击上传作品'}
                </button>

                {/* 支持的文件格式说明 */}
                <div className="text-center text-xs text-gray-600 space-y-1">
                  <p>支持PDF、DOC、DOCX、JPG、PNG、ZIP、RAR等格式文件</p>
                  <p className="text-red-500">大小不超过20M</p>
                  <p>「多个文件建议打包压缩成一个文件上传」</p>
                </div>
              </div>

              {/* 已上传作品列表 - 只有store中有数据时才显示 */}
              {portfolioItems.length > 0 && (
                <div className="mt-6">
                  <h3 className="text-base font-medium mb-3 text-gray-700">已上传的作品</h3>
                  <div className="space-y-2">
                    <DndContext
                      sensors={sensors}
                      collisionDetection={closestCenter}
                      onDragStart={handleDragStart}
                      onDragEnd={handleDragEnd}
                      measuring={{
                        droppable: {
                          strategy: MeasuringStrategy.Always,
                        },
                      }}
                    >
                      <SortableContext
                        items={portfolioItems.map(item => item.id)}
                        strategy={verticalListSortingStrategy}
                      >
                        {portfolioItems.map((item) => (
                          <SortablePortfolioItem
                            key={item.id}
                            item={item}
                            deletePortfolioItem={handleDeletePortfolio}
                            portfolioItems={portfolioItems}
                            editingId={editingId}
                            setEditingId={setEditingId}
                            updatePortfolioItem={updatePortfolioItem}
                          />
                        ))}
                      </SortableContext>

                      {/* 拖拽预览 */}
                      <DragOverlay>
                        {activeItem ? (
                          <div className="flex items-center justify-between w-full text-base text-[#2E2F66] bg-[#f7f7fc] px-4 py-2 rounded-md shadow-md">
                            <div className="flex items-center">
                              <div className="bg-white p-2 rounded-md mr-3">
                                <Image
                                  src="/image/file-icon.svg"
                                  alt="文件"
                                  width={20}
                                  height={20}
                                />
                              </div>
                              <span className="text-base">{activeItem.name}</span>
                            </div>
                          </div>
                        ) : null}
                      </DragOverlay>
                    </DndContext>
                  </div>
                </div>
              )}

              {/* 已生成的二维码 - 只有store中有数据时才显示 */}
              {portfolioItems.length > 0 && (
                <div className="mt-6">
                  <h3 className="text-base font-medium mb-3 text-gray-700">已生成的二维码</h3>
                  <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4">
                    {portfolioItems.map((item) => (
                      <div key={item.id} className="flex flex-col items-center">
                        <div className="bg-white p-4 rounded-md mb-2 w-32 h-32 flex items-center justify-center">
                          <QRCode
                            value={item.url}
                            size={100}
                            style={{ height: "auto", maxWidth: "100%", width: "100%" }}
                            viewBox={`0 0 256 256`}
                          />
                        </div>
                        <p className="text-xs text-center text-gray-600 w-32 truncate">{item.name}</p>
                      </div>
                    ))}
                  </div>
                </div>
              )}

              {/* 跳转器 */}
              <div className="mt-6">
                <ModuleNavigator currentModuleId="portfolio" />
              </div>
            </div>
          </ScrollableContent>
        </div>
      </div>

      {/* 删除确认对话框 */}
      <ItemDeleteConfirmDialog
        open={deleteConfirmOpen}
        onOpenChange={setDeleteConfirmOpen}
        itemName={itemToDelete?.name || ''}
        itemType="作品"
        onConfirm={confirmDeletePortfolio}
        disabled={portfolioItems.length <= 1}
        disabledReason="至少需要保留一个作品"
      />
    </div>
  );
}
