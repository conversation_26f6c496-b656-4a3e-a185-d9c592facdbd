'use client';

import React, { useState, useEffect, useMemo } from 'react';
import Tip from '../Tip';
import ScrollableContent from '../ScrollableContent';
import { useModuleStore, type BasicInfoItem } from '@/store/useModuleStore';
import ItemDeleteConfirmDialog from '../ItemDeleteConfirmDialog';
import Image from 'next/image';
import ModuleHeader from '@/components/ModuleHeader';
import ImageUploader from '@/components/ImageUploader';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue
} from '@/components/ui/select';
import { PlusIcon } from 'lucide-react';
import ModuleNavigator from '@/components/ModuleNavigator';

// 定义自定义字段的类型
interface CustomField {
  id: string;
  label: string;
  value: string;
}

// 定义表单数据的类型
interface FormDataType {
  name: string;
  phone: string;
  email: string;
  birth: string;
  birth_type: string;
  gender: string;
  height: string;
  weight: string;
  ethnicity: string;
  city: string;
  origin: string;
  political_affiliation: string;
  marital: string;
  avatar: string;
  job_status: string;
  intended_city: string;
  job: string;
  max_salary: string;
  site: string;
  wechat: string;
  github: string;
  gitee: string;
  customizeFields: CustomField[];
  [key: string]: string | CustomField[];
}

export default function BasicInfo() {
  // 获取基本信息模块数据和更新函数
  const { modules, updateModuleItem } = useModuleStore(state => state);
  const basicInfoModule = modules['basic_info'];

  // 使用 useMemo 包装 basicInfo 的初始化，避免在每次渲染时重新创建
  const basicInfo = useMemo(() => (basicInfoModule?.item as BasicInfoItem) || {} as BasicInfoItem, [basicInfoModule?.item]);

  // 不再需要在组件加载时激活基本信息模块，因为已经在 store 中设置了默认选中的模块

  // 创建本地状态来管理表单数据
  const [formData, setFormData] = useState<FormDataType>({
    name: '',
    phone: '',
    email: '',
    birth: '',
    birth_type: '年龄',
    gender: '',
    height: '',
    weight: '',
    ethnicity: '',
    city: '',
    origin: '',
    political_affiliation: '',
    marital: '',
    avatar: '',
    job_status: '',
    intended_city: '',
    job: '',
    max_salary: '面议',
    site: '',
    wechat: '',
    github: '',
    gitee: '',
    customizeFields: []
  });

  // 用于跟踪是否是初始化状态
  const [isInitialized, setIsInitialized] = useState(false);

  // 当basicInfo变化时，更新本地状态（仅在初始化时）
  useEffect(() => {
    if (Object.keys(basicInfo).length > 0 && !isInitialized) {
      setFormData({
        name: basicInfo.name?.value || '',
        phone: basicInfo.phone?.value || '',
        email: basicInfo.email?.value || '',
        birth: basicInfo.birth?.value || '',
        birth_type: basicInfo.birth_type?.value || '年龄',
        gender: basicInfo.gender?.value || '',
        height: basicInfo.height?.value || '',
        weight: basicInfo.weight?.value || '',
        ethnicity: basicInfo.ethnicity?.value || '',
        city: basicInfo.city?.value || '',
        origin: basicInfo.origin?.value || '',
        political_affiliation: basicInfo.political_affiliation?.value || '',
        marital: basicInfo.marital?.value || '',
        avatar: basicInfo.avatar?.value || '',
        job_status: basicInfo.job_status?.value || '',
        intended_city: basicInfo.intended_city?.value || '',
        job: basicInfo.job?.value || '',
        max_salary: basicInfo.max_salary?.value || '面议',
        site: basicInfo.site?.value || '',
        wechat: basicInfo.wechat?.value || '',
        github: basicInfo.github?.value || '',
        gitee: basicInfo.gitee?.value || '',
        customizeFields: basicInfo.customize_fields?.value || []
      });
      setIsInitialized(true);
    }
  }, [basicInfo, isInitialized]);

  // 当表单数据变化时，自动保存到store（仅在初始化后）
  useEffect(() => {
    if (isInitialized) {
      // 将表单数据转换为新的格式并保存到store
      const updatedBasicInfo: BasicInfoItem = {
        avatar: { label: "头像", value: formData.avatar },
        avatar_filter: { label: "头像滤镜", value: "" },
        birth: { label: formData.birth_type === '生日' ? '生日' : '年龄', value: formData.birth },
        birth_type: { label: "出生日期类型", value: formData.birth_type },
        city: { label: "所在城市", value: formData.city },
        created_at: basicInfo.created_at || { label: "创建时间", value: Date.now() },
        customize_fields: { label: "自定义字段", value: formData.customizeFields },
        email: { label: "邮箱", value: formData.email },
        ethnicity: { label: "民族", value: formData.ethnicity },
        gender: { label: "性别", value: formData.gender },
        gitee: { label: "Gitee", value: formData.gitee },
        github: { label: "GitHub", value: formData.github },
        height: { label: "身高", value: formData.height },
        id: basicInfo.id || { label: "ID", value: Date.now() },
        intended_city: { label: "意向城市", value: formData.intended_city },
        job: { label: "职位", value: formData.job },
        job_status: { label: "工作状态", value: formData.job_status },
        marital: { label: "婚姻状况", value: formData.marital },
        max_salary: { label: "期望薪资", value: formData.max_salary },
        name: { label: "姓名", value: formData.name },
        origin: { label: "籍贯", value: formData.origin },
        phone: { label: "电话", value: formData.phone },
        political_affiliation: { label: "政治面貌", value: formData.political_affiliation },
        site: { label: "个人网站", value: formData.site },
        updated_at: { label: "更新时间", value: Date.now() },
        wechat: { label: "微信", value: formData.wechat },
        weight: { label: "体重", value: formData.weight }
      };
      updateModuleItem('basic_info', updatedBasicInfo);
    }
  }, [formData, updateModuleItem, isInitialized, basicInfo.created_at, basicInfo.id]);

  // 图片上传器状态
  const [isImageUploaderOpen, setIsImageUploaderOpen] = useState(false);

  // 处理表单字段变化
  const handleInputChange = (field: string, value: string) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
  };



  // 处理上传头像
  const handleAvatarUpload = () => {
    // 打开图片上传器
    setIsImageUploaderOpen(true);
  };

  // 处理添加自定义字段
  const handleAddField = (fieldName: string) => {
    // 只处理自定义字段
    if (fieldName === 'custom') {
      setFormData(prev => {
        // 计算当前有多少个以"自定义"开头的字段
        const customCount = prev.customizeFields.filter(field =>
          field.label === '自定义' || field.label.match(/^自定义\s*\d+$/)
        ).length;

        // 如果已经有自定义字段，则添加编号
        const newFieldLabel = customCount > 0 ? `自定义${customCount + 1}` : '自定义';

        return {
          ...prev,
          customizeFields: [...prev.customizeFields, {
            id: Date.now().toString(), // 添加唯一ID作为key
            label: newFieldLabel,
            value: ''
          }]
        };
      });
    }
  };

  // 编辑状态管理
  const [editingFieldId, setEditingFieldId] = useState<string | null>(null);
  const [editingFieldName, setEditingFieldName] = useState('');

  // 删除确认对话框状态
  const [deleteConfirmOpen, setDeleteConfirmOpen] = useState(false);
  const [itemToDelete, setItemToDelete] = useState<{type: string, id?: string, name: string} | null>(null);

  // 处理开始编辑自定义字段名称
  const handleStartEditCustomFieldName = (fieldId: string, currentLabel: string) => {
    setEditingFieldId(fieldId);
    setEditingFieldName(currentLabel);
  };

  // 处理保存编辑的自定义字段名称
  const handleSaveCustomFieldName = () => {
    if (!editingFieldId || !editingFieldName.trim()) {
      setEditingFieldId(null);
      return;
    }

    setFormData(prev => ({
      ...prev,
      customizeFields: prev.customizeFields.map(field =>
        field.id === editingFieldId ? { ...field, label: editingFieldName.trim() } : field
      )
    }));

    setEditingFieldId(null);
  };

  // 处理取消编辑
  const handleCancelEdit = () => {
    setEditingFieldId(null);
  };

  // 这个函数已经被 handleMoveToOther 替代，不再需要
  // 保留注释以说明为什么删除

  // 显示删除确认对话框
  const showDeleteConfirm = (type: string, name: string, id?: string) => {
    setItemToDelete({ type, id, name });
    setDeleteConfirmOpen(true);
  };

  // 确认删除操作
  const confirmDelete = () => {
    if (!itemToDelete) return;

    switch (itemToDelete.type) {
      case 'avatar':
        handleInputChange('avatar', '');
        break;
      case 'customField':
        if (itemToDelete.id) {
          setFormData(prev => ({
            ...prev,
            customizeFields: prev.customizeFields.filter(field => field.id !== itemToDelete.id)
          }));
        }
        break;
      case 'site':
        handleInputChange('site', '');
        break;
      case 'wechat':
        handleInputChange('wechat', '');
        break;
      case 'github':
        handleInputChange('github', '');
        break;
      case 'gitee':
        handleInputChange('gitee', '');
        break;
    }

    // 重置删除状态
    setItemToDelete(null);
  };

  // 保留原有的删除函数以兼容现有代码
  const handleRemoveField = (fieldId: string) => {
    const field = formData.customizeFields.find(f => f.id === fieldId);
    showDeleteConfirm('customField', field?.label || '自定义字段', fieldId);
  };

  // 处理自定义字段值变化
  const handleCustomFieldChange = (fieldId: string, value: string) => {
    setFormData(prev => ({
      ...prev,
      customizeFields: prev.customizeFields.map(field =>
        field.id === fieldId ? { ...field, value } : field
      )
    }));
  };

  // 定义提示项
  const tipItems = [
    {
      question: `${new Date().getFullYear()}年了，谁还在鄙视QQ邮箱？`,
      answer: '没有人鄙视，邮箱最重要的是保证自己能及时看到，所以填你常用的方便的那个。'
    },
    {
      question: 'HR必须要看到哪些基本信息？',
      answer: '必填：姓名、电话、邮箱。\n建议填写：意向岗位、当前状态。\n其他信息根据职位确定，是加分项就填。\n如：面试国/央企，政治面貌是党员就填；面试程序员，有GitHub就填；面试运营，有自媒体账号就填。'
    },
    {
      question: '目标薪资要不要填？',
      answer: '以下情况建议不要填写：\n你的这份简历会投很多家企业；\n你所面试的岗位薪资范围比较大；\n你更在意平台和机会；\n你有很强的谈薪能力。'
    },
    {
      question: '哪些简历不要放证件照？',
      answer: '应聘外企不建议\n放英文简历不建议放\n照片不得体不建议放。'
    },
    {
      question: '企业看到你的艺术照、卡通照、宠物照会怎么想？',
      answer: '大概率会觉得幼稚，不成熟，进而影响对你工作能力的判断。\n小概率觉得很有个性，我们就需要这样的年轻人。\n所以不要去赌概率。'
    },
  ];

  // 处理编辑按钮点击
  const handleEdit = () => {
    // 将表单数据转换为新的格式并保存到store
    const updatedBasicInfo: BasicInfoItem = {
      avatar: { label: "头像", value: formData.avatar },
      avatar_filter: { label: "头像滤镜", value: "" },
      birth: { label: formData.birth_type === '生日' ? '生日' : '年龄', value: formData.birth },
      birth_type: { label: "出生日期类型", value: formData.birth_type },
      city: { label: "所在城市", value: formData.city },
      created_at: basicInfo.created_at || { label: "创建时间", value: Date.now() },
      customize_fields: { label: "自定义字段", value: formData.customizeFields },
      email: { label: "邮箱", value: formData.email },
      ethnicity: { label: "民族", value: formData.ethnicity },
      gender: { label: "性别", value: formData.gender },
      gitee: { label: "Gitee", value: formData.gitee },
      github: { label: "GitHub", value: formData.github },
      height: { label: "身高", value: formData.height },
      id: basicInfo.id || { label: "ID", value: Date.now() },
      intended_city: { label: "意向城市", value: formData.intended_city },
      job: { label: "职位", value: formData.job },
      job_status: { label: "工作状态", value: formData.job_status },
      marital: { label: "婚姻状况", value: formData.marital },
      max_salary: { label: "期望薪资", value: formData.max_salary },
      name: { label: "姓名", value: formData.name },
      origin: { label: "籍贯", value: formData.origin },
      phone: { label: "电话", value: formData.phone },
      political_affiliation: { label: "政治面貌", value: formData.political_affiliation },
      site: { label: "个人网站", value: formData.site },
      updated_at: { label: "更新时间", value: Date.now() },
      wechat: { label: "微信", value: formData.wechat },
      weight: { label: "体重", value: formData.weight }
    };
    updateModuleItem('basic_info', updatedBasicInfo);
  };



  // 处理图片上传确认
  const handleImageUploadConfirm = (imageUrl: string,
    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    filter: 'original' | 'summer' | 'vintage' | 'warm'
  ) => {
    // 将选择的图片应用到头像
    handleInputChange('avatar', imageUrl);
    // filter 参数在这里不使用，但需要保留以匹配 ImageUploader 组件的接口
  };

  return (
    <div className="h-full">
      {/* 图片上传组件 */}
      <ImageUploader
        open={isImageUploaderOpen}
        onClose={() => setIsImageUploaderOpen(false)}
        onConfirm={handleImageUploadConfirm}
        title="裁剪美化"
        maxSize={7}
      />

      <div className="flex gap-4 h-full">
        <div className="w-48">
          <Tip
            title="基本信息怎么写？"
            description="还有疑惑? 立即反馈"
            tipContent={
              <>
                填写简历的核心思路就是——扬长避短，突出自己和岗位的匹配度。面试或者简历上有缺点没关系，反而更真实，但不要暴露硬伤。
              </>
            }
            items={tipItems}
          />
        </div>

        <div className="flex-1 rounded-md flex flex-col bg-white">
          {/* 基本信息表单区域 - 支持上下滑动 */}
          <ScrollableContent>
            {/* 模块头部 */}
            <ModuleHeader
              title="基本信息"
              id="basic_info"
              description="基本信息的最大作用是让对方知道你是谁，以及如何联系你，其他非加分项信息可以考虑不写哦😉"
              icon={<Image src="/image/basic-info.svg" alt="基本信息" width={24} height={24} />}
              onEdit={handleEdit}
            />
            <div className="px-4 py-6 bg-[#f7f8fa] rounded-md">
              {/* 表单内容 - 使用网格布局 */}
              {/* 基本信息部分 - 使用栅格布局 */}
              <div className="grid grid-cols-2 gap-6">
                {/* 姓名 - 1-1 */}
                <div className="space-y-2">
                  <div className="flex items-center justify-between h-[28px]">
                    <Label htmlFor="name" className="text-base">姓名</Label>
                    {/* 添加一个空的div来保持与其他字段的对齐 */}
                    <div className="w-5 h-5"></div>
                  </div>
                  <Input
                    id="name"
                    type="text"
                    placeholder="如：张三"
                    value={formData.name}
                    onChange={(e) => handleInputChange('name', e.target.value)}
                    className="bg-white text-base md:text-base"
                  />
                </div>

                {/* 头像上传 - 1-2 & 2-2 (跨两行) */}
                <div className="space-y-2 flex flex-col items-center justify-center row-span-2 h-full">
                  <div
                    className="w-[102px] h-[130px] rounded-md bg-gray-100 flex items-center justify-center overflow-hidden border border-gray-200 cursor-pointer hover:bg-gray-50 transition-colors relative group"
                  >
                    {formData.avatar ? (
                      <>
                        <div className="w-full h-full relative">
                          <Image
                            src={formData.avatar}
                            alt="头像"
                            fill
                            sizes="102px"
                            className="object-cover"
                            style={{ objectPosition: 'center' }}
                          />
                        </div>
                        {/* 悬停时显示的操作按钮 */}
                        <div className="absolute inset-0 bg-black bg-opacity-40 flex flex-col items-center justify-center opacity-0 group-hover:opacity-100 transition-opacity">
                          <button
                            className="flex items-center gap-1 text-white mb-4 cursor-pointer hover:text-gray-200"
                            onClick={handleAvatarUpload}
                            type="button"
                          >
                            <img src="/assets/svg/edit-icon.svg" alt="更换" width={20} height={20} />
                            <span className="text-base">更换</span>
                          </button>
                          <button
                            className="flex items-center gap-1 text-white cursor-pointer hover:text-gray-200"
                            onClick={() => showDeleteConfirm('avatar', '头像')}
                            type="button"
                          >
                            <img src="/assets/svg/delete-icon.svg" alt="删除" width={20} height={20} />
                            <span className="text-base">删除</span>
                          </button>
                        </div>
                      </>
                    ) : (
                      <div
                        className="flex flex-col items-center justify-center text-gray-400"
                        onClick={handleAvatarUpload}
                      >
                        <PlusIcon className="w-10 h-10" />
                        <span className="text-base mt-2">上传头像</span>
                      </div>
                    )}
                  </div>
                </div>

                {/* 邮箱 - 2-1 */}
                <div className="space-y-2">
                  <div className="flex items-center justify-between h-[28px]">
                    <Label htmlFor="email" className="text-base">邮箱</Label>
                    {/* 添加一个空的div来保持与其他字段的对齐 */}
                    <div className="w-5 h-5"></div>
                  </div>
                  <Input
                    id="email"
                    type="email"
                    placeholder="如：<EMAIL>"
                    value={formData.email}
                    onChange={(e) => handleInputChange('email', e.target.value)}
                    className="bg-white text-base md:text-base"
                  />
                </div>

                {/* 电话 - 3-1 */}
                <div className="space-y-2">
                  <div className="flex items-center justify-between h-[28px]">
                    <Label htmlFor="phone" className="text-base">电话</Label>
                    {/* 添加一个空的div来保持与右侧性别字段的对齐 */}
                    <div className="w-5 h-5"></div>
                  </div>
                  <Input
                    id="phone"
                    type="tel"
                    placeholder="如：13800000000"
                    value={formData.phone}
                    onChange={(e) => handleInputChange('phone', e.target.value)}
                    className="bg-white text-base md:text-base"
                  />
                </div>

                {/* 性别 - 3-2 */}
                <div className="space-y-2">
                  <div className="flex items-center h-[28px]">
                    <Label htmlFor="gender" className="text-base">性别</Label>
                    <div className="w-5 h-5"></div>
                  </div>
                  <Select
                    value={formData.gender}
                    onValueChange={(value) => handleInputChange('gender', value)}
                  >
                    <SelectTrigger className="w-full bg-white text-base">
                      <SelectValue placeholder="请输入您的性别" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="男">男</SelectItem>
                      <SelectItem value="女">女</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                {/* 年龄/生日 - 4-1 */}
                <div className="space-y-2">
                  <div className="flex items-center h-[28px]">
                    <Label htmlFor="birth" className="text-base">{formData.birth_type === '生日' ? '生日' : '年龄'}</Label>
                    <div className="flex items-center gap-2 ml-auto">
                      <button
                        className={`px-2 py-1 text-xs rounded ${formData.birth_type === '年龄' ? 'bg-primary text-white' : 'bg-gray-100'}`}
                        onClick={() => handleInputChange('birth_type', '年龄')}
                      >
                        年龄
                      </button>
                      <button
                        className={`px-2 py-1 text-xs rounded ${formData.birth_type === '生日' ? 'bg-primary text-white' : 'bg-gray-100'}`}
                        onClick={() => handleInputChange('birth_type', '生日')}
                      >
                        生日
                      </button>
                    </div>
                  </div>
                  {formData.birth_type === '年龄' ? (
                    <Input
                      id="birth"
                      type="text"
                      placeholder="请输入您的年龄:如18岁"
                      value={formData.birth}
                      onChange={(e) => handleInputChange('birth', e.target.value)}
                      className="bg-white text-base md:text-base"
                    />
                  ) : (
                    <Input
                      id="birth"
                      type="date"
                      value={formData.birth}
                      onChange={(e) => handleInputChange('birth', e.target.value)}
                      className="bg-white text-base md:text-base"
                    />
                  )}
                </div>

                {/* 所在城市 - 4-2 */}
                <div className="space-y-2">
                  <div className="flex items-center h-[28px]">
                    <Label htmlFor="city" className="text-base">所在城市</Label>
                    <div className="w-5 h-5"></div>
                  </div>
                  <Input
                    id="city"
                    type="text"
                    placeholder="如：深圳"
                    value={formData.city}
                    onChange={(e) => handleInputChange('city', e.target.value)}
                    className="bg-white text-base md:text-base"
                  />
                </div>

                {/* 身高 - 5-1 */}
                <div className="space-y-2">
                  <div className="flex items-center h-[28px]">
                    <Label htmlFor="height" className="text-base">身高</Label>
                    <div className="w-5 h-5"></div>
                  </div>
                  <Input
                    id="height"
                    type="text"
                    placeholder="请输入"
                    value={formData.height}
                    onChange={(e) => handleInputChange('height', e.target.value)}
                    className="bg-white text-base md:text-base"
                  />
                </div>

                {/* 体重 - 5-2 */}
                <div className="space-y-2">
                  <div className="flex items-center h-[28px]">
                    <Label htmlFor="weight" className="text-base">体重</Label>
                    <div className="w-5 h-5"></div>
                  </div>
                  <Input
                    id="weight"
                    type="text"
                    placeholder="请输入"
                    value={formData.weight}
                    onChange={(e) => handleInputChange('weight', e.target.value)}
                    className="bg-white text-base md:text-base"
                  />
                </div>

                {/* 民族 - 6-1 */}
                <div className="space-y-2">
                  <div className="flex items-center h-[28px]">
                    <Label htmlFor="ethnicity" className="text-base">民族</Label>
                    <div className="w-5 h-5"></div>
                  </div>
                  <Input
                    id="ethnicity"
                    type="text"
                    placeholder="请输入"
                    value={formData.ethnicity}
                    onChange={(e) => handleInputChange('ethnicity', e.target.value)}
                    className="bg-white text-base md:text-base"
                  />
                </div>

                {/* 籍贯 - 6-2 */}
                <div className="space-y-2">
                  <div className="flex items-center h-[28px]">
                    <Label htmlFor="origin" className="text-base">籍贯</Label>
                    <div className="w-5 h-5"></div>
                  </div>
                  <Input
                    id="origin"
                    type="text"
                    placeholder="请输入"
                    value={formData.origin}
                    onChange={(e) => handleInputChange('origin', e.target.value)}
                    className="bg-white text-base md:text-base"
                  />
                </div>

                {/* 政治面貌 - 7-1 */}
                <div className="space-y-2">
                  <div className="flex items-center h-[28px]">
                    <Label htmlFor="political_affiliation" className="text-base">政治面貌</Label>
                    <div className="w-5 h-5"></div>
                  </div>
                  <Select
                    value={formData.political_affiliation}
                    onValueChange={(value) => handleInputChange('political_affiliation', value)}
                  >
                    <SelectTrigger className="w-full bg-white text-base">
                      <SelectValue placeholder="请输入您的政治面貌" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="中共党员">中共党员</SelectItem>
                      <SelectItem value="共青团员">共青团员</SelectItem>
                      <SelectItem value="群众">群众</SelectItem>
                      <SelectItem value="其他">其他</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                {/* 婚姻状态 - 7-2 */}
                <div className="space-y-2">
                  <div className="flex items-center h-[28px]">
                    <Label htmlFor="marital" className="text-base">婚姻状态</Label>
                    <div className="w-5 h-5"></div>
                  </div>
                  <Input
                    id="marital"
                    type="text"
                    placeholder="如：未婚"
                    value={formData.marital}
                    onChange={(e) => handleInputChange('marital', e.target.value)}
                    className="bg-white text-base md:text-base"
                  />
                </div>
              </div>

              {/* 自定义字段 */}
              {formData.customizeFields.length > 0 && (
                <div className="mt-6 grid grid-cols-2 gap-6">
                  {formData.customizeFields.map((field: { id: string, label: string, value: string }) => (
                    <div key={field.id} className="space-y-2">
                      <div className="flex items-center justify-between h-[28px]">
                        {editingFieldId === field.id ? (
                          <div className="flex-1 flex items-center">
                            <Input
                              value={editingFieldName}
                              onChange={(e) => setEditingFieldName(e.target.value)}
                              className="bg-white h-8 text-base md:text-base"
                              onBlur={handleSaveCustomFieldName}
                              onKeyDown={(e) => {
                                if (e.key === 'Enter') {
                                  handleSaveCustomFieldName();
                                } else if (e.key === 'Escape') {
                                  handleCancelEdit();
                                }
                              }}
                              autoFocus
                            />
                          </div>
                        ) : (
                          <>
                            <div className="flex items-center">
                              <Label htmlFor={`custom-${field.id}`} className="text-base">{field.label}</Label>
                              <div className="flex items-center ml-2">
                                <button
                                  className="text-gray-400 hover:text-gray-600 cursor-pointer mr-1"
                                  onClick={() => handleStartEditCustomFieldName(field.id, field.label)}
                                  type="button"
                                >
                                  <img src="/assets/svg/edit-icon.svg" alt="编辑" width={20} height={20} />
                                </button>
                                <button
                                  className="text-gray-400 hover:text-gray-600 cursor-pointer"
                                  onClick={() => handleRemoveField(field.id)}
                                  type="button"
                                >
                                  <img src="/app/assets/svg/delete-icon.svg" alt="删除" width={20} height={20} />
                                </button>
                              </div>
                            </div>
                            <div></div>
                          </>
                        )}
                      </div>
                      <Input
                        id={`custom-${field.id}`}
                        type="text"
                        value={field.value}
                        onChange={(e) => handleCustomFieldChange(field.id, e.target.value)}
                        className="bg-white text-base md:text-base"
                      />
                    </div>
                  ))}
                </div>
              )}

              {/* 求职意向部分 */}
              <div className="mt-8">
                <h3 className="text-[#707191] text-lg font-medium mb-4">求职意向</h3>
                <div className="grid grid-cols-2 gap-6">
                  {/* 当前状态 */}
                  <div className="space-y-2">
                    <Label htmlFor="job_status" className="text-base">当前状态</Label>
                    <Input
                      id="job_status"
                      type="text"
                      placeholder="如：离职-立即到岗"
                      value={formData.job_status}
                      onChange={(e) => handleInputChange('job_status', e.target.value)}
                      className="bg-white text-base md:text-base"
                    />
                  </div>

                  {/* 意向城市 */}
                  <div className="space-y-2">
                    <Label htmlFor="intended_city" className="text-base">意向城市</Label>
                    <Input
                      id="intended_city"
                      type="text"
                      placeholder="如：长沙"
                      value={formData.intended_city}
                      onChange={(e) => handleInputChange('intended_city', e.target.value)}
                      className="bg-white text-base md:text-base"
                    />
                  </div>

                  {/* 期望职位 */}
                  <div className="space-y-2">
                    <Label htmlFor="job" className="text-base">期望职位</Label>
                    <Input
                      id="job"
                      type="text"
                      placeholder="如：产品经理"
                      value={formData.job}
                      onChange={(e) => handleInputChange('job', e.target.value)}
                      className="bg-white text-base md:text-base"
                    />
                  </div>

                  {/* 期望薪资 */}
                  <div className="space-y-2">
                    <Label htmlFor="max_salary" className="text-base">期望薪资</Label>
                    <Input
                      id="max_salary"
                      type="text"
                      placeholder="如：10k-15k 或 面议"
                      value={formData.max_salary}
                      onChange={(e) => handleInputChange('max_salary', e.target.value)}
                      className="bg-white text-base md:text-base"
                    />
                  </div>
                </div>
              </div>

              {/* 社交信息部分 */}
              <div className="mt-8">
                <h3 className="text-[#707191] text-lg font-medium mb-4">社交信息</h3>

                {/* 输入框区域 - 两列布局 */}
                {(formData.site || formData.wechat || formData.github || formData.gitee) && (
                  <div className="grid grid-cols-2 gap-6 mb-6">
                    {/* 个人网站 */}
                    {formData.site && (
                      <div className="space-y-2">
                        <div className="flex items-center h-[28px]">
                          <Label htmlFor="site" className="text-base">个人网站</Label>
                          <button
                            className="ml-2 text-gray-400 hover:text-gray-600 cursor-pointer"
                            onClick={() => showDeleteConfirm('site', '个人网站')}
                            type="button"
                          >
                            <img src="/app/assets/svg/delete-icon.svg" alt="删除" width={20} height={20} />
                          </button>
                        </div>
                        <Input
                          id="site"
                          type="text"
                          placeholder="如：https://www.example.com"
                          value={formData.site}
                          onChange={(e) => handleInputChange('site', e.target.value)}
                          className="bg-white text-base md:text-base"
                        />
                      </div>
                    )}

                    {/* 微信号 */}
                    {formData.wechat && (
                      <div className="space-y-2">
                        <div className="flex items-center h-[28px]">
                          <Label htmlFor="wechat" className="text-base">微信号</Label>
                          <button
                            className="ml-2 text-gray-400 hover:text-gray-600 cursor-pointer"
                            onClick={() => showDeleteConfirm('wechat', '微信号')}
                            type="button"
                          >
                            <img src="/app/assets/svg/delete-icon.svg" alt="删除" width={20} height={20} />
                          </button>
                        </div>
                        <Input
                          id="wechat"
                          type="text"
                          placeholder="如：wxid_abc123"
                          value={formData.wechat}
                          onChange={(e) => handleInputChange('wechat', e.target.value)}
                          className="bg-white text-base md:text-base"
                        />
                      </div>
                    )}

                    {/* GitHub */}
                    {formData.github && (
                      <div className="space-y-2">
                        <div className="flex items-center h-[28px]">
                          <Label htmlFor="github" className="text-base">GitHub</Label>
                          <button
                            className="ml-2 text-gray-400 hover:text-gray-600 cursor-pointer"
                            onClick={() => showDeleteConfirm('github', 'GitHub')}
                            type="button"
                          >
                            <img src="/app/assets/svg/delete-icon.svg" alt="删除" width={20} height={20} />
                          </button>
                        </div>
                        <Input
                          id="github"
                          type="text"
                          placeholder="如：https://github.com/username"
                          value={formData.github}
                          onChange={(e) => handleInputChange('github', e.target.value)}
                          className="bg-white text-base md:text-base"
                        />
                      </div>
                    )}

                    {/* Gitee */}
                    {formData.gitee && (
                      <div className="space-y-2">
                        <div className="flex items-center h-[28px]">
                          <Label htmlFor="gitee" className="text-base">Gitee</Label>
                          <button
                            className="ml-2 text-gray-400 hover:text-gray-600 cursor-pointer"
                            onClick={() => showDeleteConfirm('gitee', 'Gitee')}
                            type="button"
                          >
                            <img src="/app/assets/svg/delete-icon.svg" alt="删除" width={20} height={20} />
                          </button>
                        </div>
                        <Input
                          id="gitee"
                          type="text"
                          placeholder="如：https://gitee.com/username"
                          value={formData.gitee}
                          onChange={(e) => handleInputChange('gitee', e.target.value)}
                          className="bg-white text-base md:text-base"
                        />
                      </div>
                    )}
                  </div>
                )}

                {/* 加号按钮区域 - 一行放多个 */}
                <div className="flex flex-wrap gap-3">
                  {!formData.site && (
                    <div
                      className="flex items-center gap-1 px-3 py-2 rounded-md border border-gray-200 cursor-pointer hover:border-primary transition-colors bg-white"
                      onClick={() => handleInputChange('site', ' ')}
                    >
                      <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 16 16" fill="none">
                        <path d="M8 3V13" stroke="#2E2F66"/>
                        <path d="M13 8L3 8" stroke="#2E2F66"/>
                      </svg>
                      <div className="text-base">个人网站</div>
                    </div>
                  )}

                  {!formData.wechat && (
                    <div
                      className="flex items-center gap-1 px-3 py-2 rounded-md border border-gray-200 cursor-pointer hover:border-primary transition-colors bg-white"
                      onClick={() => handleInputChange('wechat', ' ')}
                    >
                      <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 16 16" fill="none">
                        <path d="M8 3V13" stroke="#2E2F66"/>
                        <path d="M13 8L3 8" stroke="#2E2F66"/>
                      </svg>
                      <div className="text-base">微信号</div>
                    </div>
                  )}

                  {!formData.github && (
                    <div
                      className="flex items-center gap-1 px-3 py-2 rounded-md border border-gray-200 cursor-pointer hover:border-primary transition-colors bg-white"
                      onClick={() => handleInputChange('github', ' ')}
                    >
                      <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 16 16" fill="none">
                        <path d="M8 3V13" stroke="#2E2F66"/>
                        <path d="M13 8L3 8" stroke="#2E2F66"/>
                      </svg>
                      <div className="text-base">GitHub</div>
                    </div>
                  )}

                  {!formData.gitee && (
                    <div
                      className="flex items-center gap-1 px-3 py-2 rounded-md border border-gray-200 cursor-pointer hover:border-primary transition-colors bg-white"
                      onClick={() => handleInputChange('gitee', ' ')}
                    >
                      <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 16 16" fill="none">
                        <path d="M8 3V13" stroke="#2E2F66"/>
                        <path d="M13 8L3 8" stroke="#2E2F66"/>
                      </svg>
                      <div className="text-base">Gitee</div>
                    </div>
                  )}
                </div>
              </div>

              {/* 添加自定义字段按钮 */}
              <div className="mt-8">
                <h3 className="text-[#707191] text-lg font-medium mb-4 mt-4">自定义字段</h3>
                <div className="flex flex-wrap gap-3">
                  <div
                    className="flex items-center gap-1 px-3 py-2 rounded-md border border-gray-200 cursor-pointer hover:border-primary transition-colors bg-white"
                    onClick={() => handleAddField('custom')}
                  >
                    <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 16 16" fill="none">
                      <path d="M8 3V13" stroke="#2E2F66"/>
                      <path d="M13 8L3 8" stroke="#2E2F66"/>
                    </svg>
                    <div className="text-base">添加自定义字段</div>
                  </div>
                </div>
              </div>
            </div>

            {/* 跳转器 */}
            <div className="mt-6 pb-4">
              <ModuleNavigator currentModuleId="basic_info" />
            </div>
          </ScrollableContent>
        </div>
      </div>

      {/* 删除确认对话框 */}
      <ItemDeleteConfirmDialog
        open={deleteConfirmOpen}
        onOpenChange={setDeleteConfirmOpen}
        itemName={itemToDelete?.name || ''}
        itemType={itemToDelete?.type === 'avatar' ? '头像' :
                  itemToDelete?.type === 'customField' ? '自定义字段' :
                  itemToDelete?.name || '项目'}
        onConfirm={confirmDelete}
      />
    </div>
  );
}
