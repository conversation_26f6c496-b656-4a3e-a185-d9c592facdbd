'use client';

import React, { useState, useEffect, useRef } from 'react';
import dynamic from 'next/dynamic';
import '@toast-ui/editor/dist/toastui-editor.css';

// 定义组件的 Props 类型
interface MarkdownEditorProps {
  value?: string;
  onChange?: (value: string) => void;
  placeholder?: string;
  height?: number;
}

// 动态导入 Toast UI Editor，禁用 SSR
const Editor = dynamic(
  () => import('@toast-ui/react-editor').then(mod => mod.Editor),
  {
    ssr: false,
    loading: () => (
      <div className="h-32 bg-white rounded-md border border-input p-2">
        加载编辑器中...
      </div>
    )
  }
);

const MarkdownEditor: React.FC<MarkdownEditorProps> = ({
  value = '',
  onChange,
  placeholder = '请输入内容...',
  height
}) => {
  const [editorValue, setEditorValue] = useState(value);
  const [mounted, setMounted] = useState(false);
  const editorRef = useRef<{ getInstance: () => { getMarkdown: () => string; setMarkdown: (value: string) => void } } | null>(null);

  // 在客户端标记组件已挂载
  useEffect(() => {
    setMounted(true);
  }, []);

  // 监听value属性的变化，确保编辑器内容与外部传入的value保持同步
  useEffect(() => {
    if (value !== editorValue) {
      setEditorValue(value);
      // 如果编辑器已经初始化，更新编辑器内容
      if (editorRef.current && mounted) {
        const editorInstance = editorRef.current.getInstance();
        if (editorInstance && editorInstance.getMarkdown() !== value) {
          editorInstance.setMarkdown(value);
        }
      }
    }
  }, [value, editorValue, mounted]);

  // 处理编辑器内容变化
  const handleChange = () => {
    if (editorRef.current) {
      const editorInstance = editorRef.current.getInstance();
      // 获取 Markdown 内容以保持数据一致性
      const markdown = editorInstance.getMarkdown();
      setEditorValue(markdown);
      if (onChange) {
        onChange(markdown);
      }
    }
  };

  // 服务器端渲染或组件未挂载时显示占位符
  if (!mounted) {
    return (
      <div
        style={{ minHeight: height ? `${height}px` : '120px' }}
        className="bg-white rounded-md border border-input p-2"
      >
        加载编辑器中...
      </div>
    );
  }

  // 客户端渲染时显示编辑器
  return (
    <div>
      <Editor
        ref={editorRef}
        initialValue={editorValue}
        placeholder={placeholder}
        height="auto"
        minHeight={height ? `${height}px` : '120px'}
        initialEditType="wysiwyg"
        useCommandShortcut={true}
        hideModeSwitch={true}
        onChange={handleChange}
        usageStatistics={false}
        autofocus={false}
        toolbarItems={[
          ['heading', 'bold', 'italic', 'strike'],
          ['hr', 'quote'],
          ['ul', 'ol', 'indent', 'outdent'],
          ['link']
        ]}
      />
    </div>
  );
};

export default MarkdownEditor;
