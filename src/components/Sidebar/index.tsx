'use client';

import React, { useState, useEffect, useMemo } from 'react';
import Link from 'next/link';
import { usePathname } from 'next/navigation';
import { useUserStore } from '@/store/useUserStore';
import LoginDialog from '@/components/Auth/LoginDialog';
// 移除所有 lucide-react 图标导入，现在全部使用 SVG 图标

interface SidebarProps {
  className?: string;
}

interface MenuItem {
  id: string;
  label: string;
  icon?: React.ComponentType<{ className?: string }>;
  iconSrc?: {
    default: string;
    active: string;
  };
  href: string;
  category?: string;
}

/**
 * 通用左侧菜单组件
 */
const Sidebar: React.FC<SidebarProps> = () => {
  const pathname = usePathname();
  const [activeItem, setActiveItem] = useState('home');

  // 获取登录状态
  const { is_logged_in } = useUserStore();

  // 登录弹窗状态
  const [isLoginDialogOpen, setIsLoginDialogOpen] = useState(false);

  // 菜单项配置
  const menuItems: MenuItem[] = useMemo(() => [
    {
      id: 'my',
      label: '我的简历',
      iconSrc: {
        default: '/common/my-resumes-default.svg',
        active: '/common/my-resumes-active.svg'
      },
      href: '/my/',
      category: '简历'
    },
    {
      id: 'ai',
      label: 'AI写简历',
      iconSrc: {
        default: '/common/ai-optimize-default.svg',
        active: '/common/ai-optimize-active.svg'
      },
      href: '/ai/',
      category: '简历'
    },
    {
      id: 'ai-optimize',
      label: 'AI简历优化',
      iconSrc: {
        default: '/common/ai-optimize-default-correct.svg',
        active: '/common/ai-optimize-active-correct.svg'
      },
      href: '/youhua/',
      category: '简历'
    },
    {
      id: 'ai-score',
      label: 'AI简历打分',
      iconSrc: {
        default: '/common/ai-score-default.svg',
        active: '/common/ai-score-active.svg'
      },
      href: '/zhenduan/',
      category: '简历'
    },
    {
      id: 'templates',
      label: '简历模板',
      iconSrc: {
        default: '/common/templates-default.svg',
        active: '/common/templates-active.svg'
      },
      href: '/jianli/',
      category: '简历'
    },
    {
      id: 'fuwu',
      label: '简历服务',
      iconSrc: {
        default: '/common/resume-service-default.svg',
        active: '/common/resume-service-active.svg'
      },
      href: '/fuwu/',
      category: '服务'
    },
    // {
    //   id: 'job-guide',
    //   label: '求职攻略',
    //   iconSrc: {
    //     default: '/common/job-guide-default.svg',
    //     active: '/common/job-guide-active.svg'
    //   },
    //   href: '/job-guide',
    //   category: '攻略·资源'
    // }
  ], []);

  // 按分类分组菜单项
  const groupedMenuItems = menuItems.reduce((acc, item) => {
    const category = item.category || '其他';
    if (!acc[category]) {
      acc[category] = [];
    }
    acc[category].push(item);
    return acc;
  }, {} as Record<string, MenuItem[]>);

  // 根据当前路由设置默认选中状态
  useEffect(() => {
    if (!pathname) return;

    // 首页永远不选中任何菜单项
    if (pathname === '/') {
      setActiveItem('');
      return;
    }

    // 特殊处理个人中心页面
    if (pathname === '/user') {
      setActiveItem('user-center');
      return;
    }

    // 查找匹配当前路径的菜单项
    const matchedItem = menuItems.find(item => {
      // 精确匹配
      if (item.href === pathname) {
        return true;
      }
      // 对于 /jianli 路径，也匹配 /jianli/xxx 的情况
      if (item.href === '/jianli' && pathname.startsWith('/jianli')) {
        return true;
      }
      return false;
    });

    if (matchedItem) {
      setActiveItem(matchedItem.id);
    } else {
      // 如果没有匹配的菜单项，清空选中状态
      setActiveItem('');
    }
  }, [pathname, menuItems]);

  // 需要登录才能访问的菜单项ID
  const requireLoginItemIds = ['my', 'ai', 'ai-optimize', 'ai-score'];

  const handleItemClick = (itemId: string) => {
    setActiveItem(itemId);
  };

  // 处理需要登录验证的菜单项点击
  const handleMenuItemClick = (e: React.MouseEvent, item: MenuItem) => {
    // 检查是否需要登录验证
    if (requireLoginItemIds.includes(item.id)) {
      if (!is_logged_in) {
        e.preventDefault();
        setIsLoginDialogOpen(true);
        return;
      }
    }
    handleItemClick(item.id);
  };

  // 处理个人中心点击
  const handleUserCenterClick = (e: React.MouseEvent) => {
    if (!is_logged_in) {
      e.preventDefault();
      setIsLoginDialogOpen(true);
      return;
    }
    handleItemClick('user-center');
  };

  return (
    <div className="w-56 bg-muted h-full">
      {/* 首页和个人中心菜单项 */}
      <div className="pt-4 pb-2 px-3 space-y-1">
        <Link
          href="/"
          onClick={() => handleItemClick('')}
          className="flex items-center px-3 py-2 text-base font-normal rounded-lg transition-colors text-gray-700 hover:bg-gray-50 hover:text-gray-900"
        >
          <img src="/common/home-icon.svg" alt="首页" className="w-5 h-5 mr-3" />
          首页
        </Link>

        <Link
          href="/user"
          onClick={handleUserCenterClick}
          className={`flex items-center px-3 py-2 text-base font-normal rounded-lg transition-colors ${
            activeItem === 'user-center'
              ? 'bg-[#f5f3ff] text-[#824dfc]'
              : 'text-gray-700 hover:bg-gray-50 hover:text-gray-900'
          }`}
        >
          <img
            src={activeItem === 'user-center' ? '/common/user-center-active.svg' : '/common/user-center-default.svg'}
            alt="个人中心"
            className="w-5 h-5 mr-3"
          />
          个人中心
        </Link>
      </div>

      {/* 菜单列表 */}
      <div className="py-4">
        {Object.entries(groupedMenuItems).map(([category, items]) => (
          <div key={category} className="mb-6">
            {/* 分类标题 - 只有当分类不为空时才显示 */}
            {category && (
              <div className="px-6 mb-3">
                <h3 className="text-sm font-medium text-gray-500 uppercase tracking-wider">
                  {category}
                </h3>
              </div>
            )}

            {/* 菜单项 */}
            <div className="space-y-1 px-3">
              {items.map((item) => {
                const isActive = activeItem === item.id;

                return (
                  <Link
                    key={item.id}
                    href={item.href}
                    onClick={(e) => handleMenuItemClick(e, item)}
                    className={`flex items-center px-3 py-2 text-base font-normal rounded-lg transition-colors ${
                      isActive
                        ? 'bg-[#f5f3ff] text-[#824dfc]'
                        : 'text-gray-700 hover:bg-gray-50 hover:text-gray-900'
                    }`}
                  >
                    {/* 渲染图标 */}
                    {item.iconSrc ? (
                      <img
                        src={isActive ? item.iconSrc.active : item.iconSrc.default}
                        alt={item.label}
                        className="w-5 h-5 mr-3"
                      />
                    ) : item.icon ? (
                      <item.icon className={`w-5 h-5 mr-3 ${isActive ? 'text-[#824dfc]' : 'text-gray-400'}`} />
                    ) : null}
                    {item.label}
                  </Link>
                );
              })}
            </div>
          </div>
        ))}
      </div>

      {/* 登录弹窗 */}
      <LoginDialog
        isOpen={isLoginDialogOpen}
        onClose={() => setIsLoginDialogOpen(false)}
      />
    </div>
  );
};

export default Sidebar;
