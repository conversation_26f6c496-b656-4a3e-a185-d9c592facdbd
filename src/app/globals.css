@tailwind base;
@tailwind components;
@tailwind utilities;
@import "tw-animate-css";

:root {
  --radius: 0.625rem;
  --background: #ffffff;
  --foreground: #1f2937;
  --card: #ffffff;
  --card-foreground: #1f2937;
  --popover: #ffffff;
  --popover-foreground: #1f2937;
  --primary: #824dfc;
  --primary-foreground: #ffffff;
  --secondary: #f3f4f6;
  --secondary-foreground: #1f2937;
  --muted: #f3f4f6;
  --muted-foreground: #6b7280;
  --accent: #9d7afd;
  --accent-foreground: #1f2937;
  --destructive: #ef4444;
  --border: #e5e7eb;
  --input: #e5e7eb;
  --ring: #824dfc;
  --chart-1: #824dfc;
  --chart-2: #9d7afd;
  --chart-3: #C4B5FD;
  --chart-4: #DDD6FE;
  --chart-5: #EDE9FE;
  --sidebar: #ffffff;
  --sidebar-foreground: #1f2937;
  --sidebar-primary: #824dfc;
  --sidebar-primary-foreground: #ffffff;
  --sidebar-accent: #f3f4f6;
  --sidebar-accent-foreground: #1f2937;
  --sidebar-border: #e5e7eb;
  --sidebar-ring: #824dfc;
}

.dark {
  --background: #111827;
  --foreground: #f9fafb;
  --card: #1f2937;
  --card-foreground: #f9fafb;
  --popover: #1f2937;
  --popover-foreground: #f9fafb;
  --primary: #9d7afd;
  --primary-foreground: #111827;
  --secondary: #374151;
  --secondary-foreground: #f9fafb;
  --muted: #374151;
  --muted-foreground: #9ca3af;
  --accent: #824dfc;
  --accent-foreground: #f9fafb;
  --destructive: #f87171;
  --border: rgba(249, 250, 251, 0.1);
  --input: rgba(249, 250, 251, 0.15);
  --ring: #9d7afd;
  --chart-1: #9d7afd;
  --chart-2: #824dfc;
  --chart-3: #7C3AED;
  --chart-4: #6D28D9;
  --chart-5: #5B21B6;
  --sidebar: #1f2937;
  --sidebar-foreground: #f9fafb;
  --sidebar-primary: #9d7afd;
  --sidebar-primary-foreground: #f9fafb;
  --sidebar-accent: #374151;
  --sidebar-accent-foreground: #f9fafb;
  --sidebar-border: rgba(249, 250, 251, 0.1);
  --sidebar-ring: #9d7afd;
}

@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground;
  }
}

/* 自定义容器样式 */
.container-custom {
  width: 100%;
  margin: 0 auto;
  padding: 9px 24px;
}

/* 媒体查询 */
@media screen and (min-width: 1920px) {
  .container-custom {
    width: 1800px;
    padding: 9px 0;
  }
}

@media screen and (max-width: 1920px) {
  .container-custom {
    padding: 9px 60px;
  }
}

@media screen and (max-width: 1600px) {
  .container-custom {
    padding: 9px 36px;
  }
}

@media screen and (max-width: 1440px) {
  .container-custom {
    padding: 9px 24px;
  }
}

/* 弹窗关闭按钮样式 */
[role="dialog"] button {
  cursor: pointer !important;
}

/* 更具体的选择器，确保只选择关闭按钮 */
[role="dialog"] button[type="button"]:has(svg.lucide-x) {
  cursor: pointer !important;
}

/* 搜索下拉框动画 */
@keyframes dropdown-slide-in {
  from {
    opacity: 0;
    transform: translateY(-8px) scale(0.98);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

.animate-dropdown-slide-in {
  animation: dropdown-slide-in 0.2s ease-out;
}

/* 自定义滚动条样式 */
.custom-scrollbar {
  scrollbar-width: thin;
  scrollbar-color: rgba(130, 77, 252, 0.15) transparent;
}

.custom-scrollbar::-webkit-scrollbar {
  width: 4px;
}

.custom-scrollbar::-webkit-scrollbar-track {
  background: transparent;
}

.custom-scrollbar::-webkit-scrollbar-thumb {
  background: rgba(130, 77, 252, 0.15);
  border-radius: 2px;
}

.custom-scrollbar::-webkit-scrollbar-thumb:hover {
  background: rgba(130, 77, 252, 0.3);
}

/* 隐藏滚动条但保持滚动功能 */
.hide-scrollbar {
  scrollbar-width: none; /* Firefox */
  -ms-overflow-style: none; /* IE/Edge */
}

.hide-scrollbar::-webkit-scrollbar {
  display: none; /* Chrome/Safari/Webkit */
}

/* AI问答动画效果 */
@keyframes fade-in-up {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.animate-fade-in-up {
  animation: fade-in-up 0.6s ease-out;
}

/* 问题出现时的延迟动画 */
@keyframes question-appear {
  0% {
    opacity: 0;
    transform: translateY(30px) scale(0.95);
  }
  60% {
    opacity: 0.8;
    transform: translateY(-5px) scale(1.02);
  }
  100% {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

.animate-question-appear {
  animation: question-appear 0.8s cubic-bezier(0.34, 1.56, 0.64, 1);
}

/* 用户反馈气泡动画 */
@keyframes bubble-in {
  0% {
    opacity: 0;
    transform: translateX(20px) scale(0.8);
  }
  50% {
    opacity: 0.8;
    transform: translateX(-3px) scale(1.05);
  }
  100% {
    opacity: 1;
    transform: translateX(0) scale(1);
  }
}

.animate-bubble-in {
  animation: bubble-in 0.5s ease-out;
}

/* Tab切换动画 */
@keyframes tab-fade-in {
  0% {
    opacity: 0;
    transform: translateY(10px);
  }
  100% {
    opacity: 1;
    transform: translateY(0);
  }
}

.animate-tab-fade-in {
  animation: tab-fade-in 0.3s ease-out;
}

/* Sonner Toast 主题色样式 */
[data-sonner-toast][data-type="success"] {
  background-color: var(--primary) !important;
  color: var(--primary-foreground) !important;
  border-color: var(--primary) !important;
}

[data-sonner-toast][data-type="error"] {
  background-color: var(--destructive) !important;
  color: white !important;
  border-color: var(--destructive) !important;
}

[data-sonner-toast][data-type="warning"] {
  background-color: #f59e0b !important;
  color: white !important;
  border-color: #f59e0b !important;
}

[data-sonner-toast][data-type="info"] {
  background-color: #3b82f6 !important;
  color: white !important;
  border-color: #3b82f6 !important;
}

/* 确保 success toast 使用主题色 */
[data-sonner-toaster] [data-type="success"] {
  background: var(--primary) !important;
  color: var(--primary-foreground) !important;
  border: 1px solid var(--primary) !important;
}

/* 确保 error toast 使用错误色 */
[data-sonner-toaster] [data-type="error"] {
  background: var(--destructive) !important;
  color: white !important;
  border: 1px solid var(--destructive) !important;
}
